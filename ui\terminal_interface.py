"""
Modern Terminal Interface for Enhanced AI Coding Agent.

Provides a beautiful, clean terminal interface similar to opencode with:
- Modern design and layout
- Real-time updates
- Syntax highlighting
- Interactive components
- Professional appearance
"""

import os
import sys
import asyncio
import threading
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from pathlib import Path

try:
    from rich.console import Console
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.text import Text
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.syntax import Syntax
    from rich.markdown import Markdown
    from rich.live import Live
    from rich.align import Align
    from rich.columns import Columns
    from rich.tree import Tree
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    import prompt_toolkit
    from prompt_toolkit import Application
    from prompt_toolkit.layout import Layout as PTLayout
    from prompt_toolkit.layout.containers import HSplit, VSplit, Window
    from prompt_toolkit.layout.controls import FormattedTextControl
    from prompt_toolkit.widgets import <PERSON><PERSON><PERSON>, Frame
    from prompt_toolkit.key_binding import KeyBindings
    from prompt_toolkit.shortcuts import input_dialog, message_dialog
    PROMPT_TOOLKIT_AVAILABLE = True
except ImportError:
    PROMPT_TOOLKIT_AVAILABLE = False

from .chat_interface import ChatInterface
from .file_explorer import FileExplorer
from .code_editor import CodeEditor


class ModernTerminalInterface:
    """
    Modern terminal interface with opencode-like design.
    
    Features:
    - Clean, modern layout
    - Real-time chat interface
    - Integrated file explorer
    - Code editor with syntax highlighting
    - Terminal integration
    - Professional appearance
    """
    
    def __init__(self, workspace_path: Optional[str] = None):
        """Initialize the modern terminal interface."""
        self.workspace_path = workspace_path or os.getcwd()
        self.console = Console() if RICH_AVAILABLE else None
        self.layout = None
        self.live = None
        self.running = False
        
        # Initialize components
        self.chat_interface = ChatInterface()
        self.file_explorer = FileExplorer(self.workspace_path)
        self.code_editor = CodeEditor()
        
        # State management
        self.current_file = None
        self.chat_history = []
        self.status_message = "Ready"
        self.active_panel = "chat"  # chat, files, editor, terminal
        
        # Setup layout
        self._setup_layout()
    
    def _setup_layout(self):
        """Setup the terminal layout."""
        if not RICH_AVAILABLE:
            return
        
        # Create main layout
        self.layout = Layout()
        
        # Split into header, body, and footer
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="body"),
            Layout(name="footer", size=3)
        )
        
        # Split body into sidebar and main content
        self.layout["body"].split_row(
            Layout(name="sidebar", size=30),
            Layout(name="main")
        )
        
        # Split main into chat and editor
        self.layout["main"].split_column(
            Layout(name="chat", ratio=2),
            Layout(name="editor", ratio=1)
        )
        
        # Update layout content
        self._update_layout()
    
    def _update_layout(self):
        """Update the layout content."""
        if not RICH_AVAILABLE or not self.layout:
            return
        
        # Header
        header_text = Text("🚀 Enhanced AI Coding Agent", style="bold blue")
        header_text.append(" | ", style="dim")
        header_text.append(f"Workspace: {os.path.basename(self.workspace_path)}", style="green")
        header_text.append(" | ", style="dim")
        header_text.append(f"Status: {self.status_message}", style="yellow")
        
        self.layout["header"].update(
            Panel(
                Align.center(header_text),
                style="blue",
                title="AI Coding Agent"
            )
        )
        
        # Sidebar - File Explorer
        file_tree = self.file_explorer.get_tree_display()
        self.layout["sidebar"].update(
            Panel(
                file_tree,
                title="📁 File Explorer",
                border_style="green"
            )
        )
        
        # Chat Interface
        chat_content = self.chat_interface.get_display()
        self.layout["chat"].update(
            Panel(
                chat_content,
                title="💬 AI Assistant",
                border_style="blue"
            )
        )
        
        # Code Editor
        editor_content = self.code_editor.get_display(self.current_file)
        self.layout["editor"].update(
            Panel(
                editor_content,
                title=f"📝 Editor: {os.path.basename(self.current_file) if self.current_file else 'No file'}",
                border_style="magenta"
            )
        )
        
        # Footer
        footer_text = Text("Commands: ", style="dim")
        footer_text.append("[Ctrl+C] Exit", style="red")
        footer_text.append(" | ", style="dim")
        footer_text.append("[Tab] Switch Panel", style="cyan")
        footer_text.append(" | ", style="dim")
        footer_text.append("[Enter] Send Message", style="green")
        footer_text.append(" | ", style="dim")
        footer_text.append(f"Active: {self.active_panel.title()}", style="yellow")
        
        self.layout["footer"].update(
            Panel(
                Align.center(footer_text),
                style="dim"
            )
        )
    
    def start(self):
        """Start the modern terminal interface."""
        if not RICH_AVAILABLE:
            self._start_fallback_interface()
            return
        
        self.running = True
        
        try:
            with Live(self.layout, refresh_per_second=4, screen=True) as live:
                self.live = live
                self._run_interface_loop()
        except KeyboardInterrupt:
            self.stop()
    
    def _start_fallback_interface(self):
        """Start a fallback interface when rich is not available."""
        print("🚀 Enhanced AI Coding Agent - Terminal Interface")
        print("=" * 60)
        print(f"Workspace: {self.workspace_path}")
        print("Note: Install 'rich' package for enhanced UI experience")
        print("=" * 60)
        
        while True:
            try:
                user_input = input("\n💬 You: ")
                if user_input.lower() in ['exit', 'quit', 'q']:
                    break
                
                print("🤖 AI: Processing your request...")
                # This would integrate with the chat system
                
            except KeyboardInterrupt:
                break
        
        print("\nGoodbye! 👋")
    
    def _run_interface_loop(self):
        """Run the main interface loop."""
        import time
        
        while self.running:
            try:
                # Update layout
                self._update_layout()
                
                # Handle input (simplified for now)
                time.sleep(0.1)
                
            except KeyboardInterrupt:
                self.stop()
                break
    
    def stop(self):
        """Stop the terminal interface."""
        self.running = False
        if self.live:
            self.live.stop()
    
    def send_message(self, message: str):
        """Send a message to the AI assistant."""
        self.chat_interface.add_user_message(message)
        self.status_message = "Processing..."
        
        # This would integrate with the enhanced agent
        response = f"AI response to: {message}"
        self.chat_interface.add_ai_message(response)
        self.status_message = "Ready"
    
    def open_file(self, file_path: str):
        """Open a file in the editor."""
        if os.path.exists(file_path):
            self.current_file = file_path
            self.code_editor.load_file(file_path)
            self.status_message = f"Opened: {os.path.basename(file_path)}"
    
    def switch_panel(self, panel: str):
        """Switch the active panel."""
        if panel in ["chat", "files", "editor", "terminal"]:
            self.active_panel = panel
            self.status_message = f"Switched to {panel}"
    
    def show_help(self):
        """Show help information."""
        help_text = """
🚀 Enhanced AI Coding Agent - Help

Commands:
- Type messages to chat with the AI assistant
- Use Tab to switch between panels
- Click on files to open them in the editor
- Ctrl+C to exit

Panels:
- Chat: Interact with the AI assistant
- Files: Browse and select project files
- Editor: View and edit code files
- Terminal: Integrated terminal (coming soon)

Features:
- Intelligent code suggestions
- Project-wide understanding
- Advanced reasoning capabilities
- Multi-file coordination
- Research and documentation integration
"""
        self.chat_interface.add_system_message(help_text)
    
    def get_status(self) -> Dict[str, Any]:
        """Get current interface status."""
        return {
            "running": self.running,
            "workspace": self.workspace_path,
            "current_file": self.current_file,
            "active_panel": self.active_panel,
            "status": self.status_message,
            "chat_messages": len(self.chat_history),
            "rich_available": RICH_AVAILABLE,
            "prompt_toolkit_available": PROMPT_TOOLKIT_AVAILABLE
        }


class InteractiveTerminal:
    """
    Interactive terminal with prompt_toolkit for advanced input handling.
    """
    
    def __init__(self, interface: ModernTerminalInterface):
        """Initialize interactive terminal."""
        self.interface = interface
        self.app = None
        
        if PROMPT_TOOLKIT_AVAILABLE:
            self._setup_prompt_toolkit()
    
    def _setup_prompt_toolkit(self):
        """Setup prompt_toolkit application."""
        # Create text areas
        self.input_area = TextArea(
            prompt="💬 You: ",
            multiline=False,
            wrap_lines=False
        )
        
        self.output_area = TextArea(
            text="Welcome to Enhanced AI Coding Agent!\nType your message below and press Enter.\n",
            read_only=True,
            scrollbar=True,
            wrap_lines=True
        )
        
        # Create layout
        container = HSplit([
            Frame(self.output_area, title="Chat"),
            Frame(self.input_area, title="Input")
        ])
        
        # Key bindings
        kb = KeyBindings()
        
        @kb.add('enter')
        def _(event):
            """Handle Enter key."""
            text = self.input_area.text
            if text.strip():
                self._process_input(text)
                self.input_area.text = ""
        
        @kb.add('c-c')
        def _(event):
            """Handle Ctrl+C."""
            event.app.exit()
        
        @kb.add('c-q')
        def _(event):
            """Handle Ctrl+Q."""
            event.app.exit()
        
        # Create application
        self.app = Application(
            layout=PTLayout(container),
            key_bindings=kb,
            full_screen=True
        )
    
    def _process_input(self, text: str):
        """Process user input."""
        # Add to output
        self.output_area.text += f"\n💬 You: {text}\n"
        
        # Process with AI (simplified)
        response = f"🤖 AI: I received your message: '{text}'"
        self.output_area.text += f"{response}\n"
        
        # Scroll to bottom
        self.output_area.buffer.cursor_position = len(self.output_area.text)
    
    def run(self):
        """Run the interactive terminal."""
        if self.app:
            self.app.run()
        else:
            print("Prompt toolkit not available. Using fallback interface.")
            self.interface._start_fallback_interface()


def create_modern_interface(workspace_path: Optional[str] = None) -> ModernTerminalInterface:
    """
    Create and return a modern terminal interface.
    
    Args:
        workspace_path: Path to the workspace directory
    
    Returns:
        ModernTerminalInterface instance
    """
    return ModernTerminalInterface(workspace_path)


def run_interactive_mode(workspace_path: Optional[str] = None):
    """
    Run the interactive terminal mode.
    
    Args:
        workspace_path: Path to the workspace directory
    """
    interface = create_modern_interface(workspace_path)
    
    if PROMPT_TOOLKIT_AVAILABLE:
        terminal = InteractiveTerminal(interface)
        terminal.run()
    else:
        interface.start()


if __name__ == "__main__":
    # Demo mode
    run_interactive_mode()
