"""
Enhanced Agent Orchestrator - Main coordination system for the enhanced AI coding agent.

This module integrates all enhanced capabilities including:
- Sequential thinking and Chain of Thought reasoning
- Enhanced tool discovery and integration
- Advanced research capabilities
- Multi-file coordination and project-level reasoning
- Self-critique and reflection mechanisms
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Generator, <PERSON><PERSON>
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from chat import chat, generate_response, create_initial_messages
from message import Message, create_user_message, create_system_message
from tools import init_tools, get_tools_for_llm, execute_msg
from tools.enhanced_tool_discovery import get_enhanced_discovery, ToolRecommendation
from tools.sequential_thinking import SequentialThinkingTool, ThinkingType
from tools.enhanced_research import EnhancedResearchTool, ResearchType
from tools.advanced_codebase_analyzer import AdvancedCodebaseAnalyzer
from config import get_config
from conversation import ConversationManager

logger = logging.getLogger(__name__)


class AgentMode(Enum):
    """Different modes of agent operation."""
    INTERACTIVE = "interactive"
    AUTONOMOUS = "autonomous"
    RESEARCH = "research"
    ANALYSIS = "analysis"
    DEVELOPMENT = "development"
    DEBUGGING = "debugging"


class TaskComplexity(Enum):
    """Task complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    EXPERT = "expert"


@dataclass
class AgentContext:
    """Context information for the enhanced agent."""
    current_task: str = ""
    task_complexity: TaskComplexity = TaskComplexity.MODERATE
    agent_mode: AgentMode = AgentMode.INTERACTIVE
    workspace_path: str = ""
    conversation_history: List[Message] = field(default_factory=list)
    active_tools: List[str] = field(default_factory=list)
    research_context: Dict[str, Any] = field(default_factory=dict)
    codebase_context: Dict[str, Any] = field(default_factory=dict)
    thinking_sessions: List[str] = field(default_factory=list)
    confidence_score: float = 0.8
    metadata: Dict[str, Any] = field(default_factory=dict)


class EnhancedAgentOrchestrator:
    """
    Enhanced Agent Orchestrator with comprehensive capabilities.
    
    Features:
    - Intelligent task analysis and complexity assessment
    - Dynamic tool selection and recommendation
    - Chain of Thought reasoning integration
    - Multi-step planning and execution
    - Self-critique and improvement mechanisms
    - Research and documentation integration
    - Advanced codebase understanding
    """
    
    def __init__(self):
        """Initialize the enhanced agent orchestrator."""
        self.config = get_config()
        self.tool_discovery = get_enhanced_discovery()
        self.sequential_thinking = SequentialThinkingTool()
        self.research_tool = EnhancedResearchTool()
        self.codebase_analyzer = AdvancedCodebaseAnalyzer()
        
        # Initialize enhanced capabilities
        self.conversation_manager = None
        self.active_context = AgentContext()
        self.performance_metrics = {
            "tasks_completed": 0,
            "average_confidence": 0.0,
            "tool_usage_stats": {},
            "thinking_sessions": 0,
            "research_queries": 0
        }
    
    def process_user_request(
        self,
        user_input: str,
        conversation_id: Optional[str] = None,
        workspace_path: Optional[str] = None
    ) -> Generator[Message, None, None]:
        """
        Process a user request with enhanced capabilities.
        
        Args:
            user_input: User's request or question
            conversation_id: Optional conversation ID for persistence
            workspace_path: Optional workspace path
        
        Yields:
            Response messages from the enhanced agent
        """
        try:
            # Initialize conversation manager
            if not self.conversation_manager:
                self.conversation_manager = ConversationManager(conversation_id)
            
            # Update context
            self.active_context.current_task = user_input
            self.active_context.workspace_path = workspace_path or ""
            
            yield Message(
                role="assistant",
                content="🤖 **Enhanced AI Agent Activated**\n\n"
                       "Analyzing your request with advanced capabilities...\n"
            )
            
            # Step 1: Analyze task complexity and determine approach
            yield from self._analyze_task_complexity(user_input)
            
            # Step 2: Determine optimal agent mode
            yield from self._determine_agent_mode(user_input)
            
            # Step 3: Get tool recommendations
            yield from self._get_tool_recommendations(user_input)
            
            # Step 4: Apply Chain of Thought reasoning if needed
            if self.active_context.task_complexity in [TaskComplexity.COMPLEX, TaskComplexity.EXPERT]:
                yield from self._apply_sequential_thinking(user_input)
            
            # Step 5: Execute the main task
            yield from self._execute_main_task(user_input)
            
            # Step 6: Self-critique and improvement
            yield from self._self_critique_and_improve()
            
            # Step 7: Update performance metrics
            self._update_performance_metrics()
            
            yield Message(
                role="assistant",
                content=f"\n✅ **Task completed with {self.active_context.confidence_score:.1%} confidence**\n"
                       f"Mode: {self.active_context.agent_mode.value} | "
                       f"Complexity: {self.active_context.task_complexity.value}\n"
            )
            
        except Exception as e:
            logger.error(f"Error in enhanced agent orchestrator: {e}")
            yield Message(
                role="assistant",
                content=f"❌ **Error in enhanced processing:** {str(e)}\n"
                       "Falling back to standard processing...\n"
            )
            
            # Fallback to standard chat
            yield from self._fallback_processing(user_input, conversation_id, workspace_path)
    
    def _analyze_task_complexity(self, user_input: str) -> Generator[Message, None, None]:
        """Analyze the complexity of the user's task."""
        yield Message(
            role="assistant",
            content="🔍 **Analyzing task complexity...**\n"
        )
        
        # Complexity indicators
        complexity_indicators = {
            TaskComplexity.SIMPLE: ["help", "what is", "how to", "explain", "show"],
            TaskComplexity.MODERATE: ["create", "build", "implement", "fix", "debug"],
            TaskComplexity.COMPLEX: ["design", "architect", "optimize", "refactor", "analyze"],
            TaskComplexity.EXPERT: ["enhance", "transform", "integrate", "coordinate", "orchestrate"]
        }
        
        user_lower = user_input.lower()
        complexity_scores = {}
        
        for complexity, indicators in complexity_indicators.items():
            score = sum(1 for indicator in indicators if indicator in user_lower)
            complexity_scores[complexity] = score
        
        # Determine complexity based on highest score
        if complexity_scores[TaskComplexity.EXPERT] > 0:
            self.active_context.task_complexity = TaskComplexity.EXPERT
        elif complexity_scores[TaskComplexity.COMPLEX] > 0:
            self.active_context.task_complexity = TaskComplexity.COMPLEX
        elif complexity_scores[TaskComplexity.MODERATE] > 0:
            self.active_context.task_complexity = TaskComplexity.MODERATE
        else:
            self.active_context.task_complexity = TaskComplexity.SIMPLE
        
        # Additional complexity factors
        if len(user_input.split()) > 20:  # Long requests are typically more complex
            if self.active_context.task_complexity == TaskComplexity.SIMPLE:
                self.active_context.task_complexity = TaskComplexity.MODERATE
            elif self.active_context.task_complexity == TaskComplexity.MODERATE:
                self.active_context.task_complexity = TaskComplexity.COMPLEX
        
        yield Message(
            role="assistant",
            content=f"📊 **Task Complexity:** {self.active_context.task_complexity.value.title()}\n"
        )
    
    def _determine_agent_mode(self, user_input: str) -> Generator[Message, None, None]:
        """Determine the optimal agent mode for the task."""
        yield Message(
            role="assistant",
            content="🎯 **Determining optimal agent mode...**\n"
        )
        
        user_lower = user_input.lower()
        
        # Mode indicators
        if any(word in user_lower for word in ["research", "search", "find", "lookup", "documentation"]):
            self.active_context.agent_mode = AgentMode.RESEARCH
        elif any(word in user_lower for word in ["analyze", "review", "examine", "understand"]):
            self.active_context.agent_mode = AgentMode.ANALYSIS
        elif any(word in user_lower for word in ["build", "create", "implement", "develop"]):
            self.active_context.agent_mode = AgentMode.DEVELOPMENT
        elif any(word in user_lower for word in ["debug", "fix", "error", "bug", "issue"]):
            self.active_context.agent_mode = AgentMode.DEBUGGING
        elif any(word in user_lower for word in ["autonomous", "complete", "end-to-end"]):
            self.active_context.agent_mode = AgentMode.AUTONOMOUS
        else:
            self.active_context.agent_mode = AgentMode.INTERACTIVE
        
        yield Message(
            role="assistant",
            content=f"🤖 **Agent Mode:** {self.active_context.agent_mode.value.title()}\n"
        )
    
    def _get_tool_recommendations(self, user_input: str) -> Generator[Message, None, None]:
        """Get intelligent tool recommendations."""
        yield Message(
            role="assistant",
            content="🛠️ **Getting tool recommendations...**\n"
        )
        
        # Get recommendations from enhanced tool discovery
        recommendations = self.tool_discovery.recommend_tools(
            context=user_input,
            current_task=self.active_context.current_task,
            conversation_history=self.active_context.conversation_history,
            max_recommendations=5
        )
        
        if recommendations:
            yield Message(
                role="assistant",
                content="**Recommended Tools:**\n"
            )
            
            for i, rec in enumerate(recommendations, 1):
                yield Message(
                    role="assistant",
                    content=f"{i}. **{rec.tool_name}** (confidence: {rec.confidence:.1%})\n"
                           f"   {rec.reasoning}\n"
                )
                
                # Add to active tools if confidence is high
                if rec.confidence > 0.7:
                    self.active_context.active_tools.append(rec.tool_name)
        else:
            yield Message(
                role="assistant",
                content="Using standard tool set for this task.\n"
            )
    
    def _apply_sequential_thinking(self, user_input: str) -> Generator[Message, None, None]:
        """Apply Chain of Thought reasoning for complex tasks."""
        yield Message(
            role="assistant",
            content="🧠 **Applying Chain of Thought reasoning...**\n"
        )
        
        # Determine thinking type based on task
        thinking_type = ThinkingType.CHAIN_OF_THOUGHT
        if "critique" in user_input.lower() or "review" in user_input.lower():
            thinking_type = ThinkingType.SELF_CRITIQUE
        elif "plan" in user_input.lower() or "strategy" in user_input.lower():
            thinking_type = ThinkingType.PLANNING
        elif "break down" in user_input.lower() or "decompose" in user_input.lower():
            thinking_type = ThinkingType.PROBLEM_DECOMPOSITION
        
        # Execute sequential thinking
        thinking_messages = self.sequential_thinking.execute(
            content=user_input,
            thinking_type=thinking_type.value,
            max_iterations=8,
            context=f"Agent mode: {self.active_context.agent_mode.value}",
            goal="Provide comprehensive solution with high confidence"
        )
        
        for message in thinking_messages:
            yield message
        
        # Track thinking session
        session_id = f"thinking_{len(self.active_context.thinking_sessions)}"
        self.active_context.thinking_sessions.append(session_id)
        self.performance_metrics["thinking_sessions"] += 1
    
    def _execute_main_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute the main task based on determined mode and complexity."""
        yield Message(
            role="assistant",
            content="⚡ **Executing main task...**\n"
        )
        
        if self.active_context.agent_mode == AgentMode.RESEARCH:
            yield from self._execute_research_task(user_input)
        elif self.active_context.agent_mode == AgentMode.ANALYSIS:
            yield from self._execute_analysis_task(user_input)
        elif self.active_context.agent_mode == AgentMode.DEVELOPMENT:
            yield from self._execute_development_task(user_input)
        elif self.active_context.agent_mode == AgentMode.DEBUGGING:
            yield from self._execute_debugging_task(user_input)
        elif self.active_context.agent_mode == AgentMode.AUTONOMOUS:
            yield from self._execute_autonomous_task(user_input)
        else:
            yield from self._execute_interactive_task(user_input)
    
    def _execute_research_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute research-focused task."""
        research_messages = self.research_tool.execute(
            content=user_input,
            research_type=ResearchType.WEB_SEARCH.value,
            max_sources=10,
            include_code=True,
            context=f"Agent mode: research, Complexity: {self.active_context.task_complexity.value}"
        )
        
        for message in research_messages:
            yield message
        
        self.performance_metrics["research_queries"] += 1
    
    def _execute_analysis_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute analysis-focused task."""
        if self.active_context.workspace_path:
            analysis_messages = self.codebase_analyzer.execute(
                content=user_input,
                project_root=self.active_context.workspace_path,
                analysis_type="comprehensive",
                include_metrics=True
            )
            
            for message in analysis_messages:
                yield message
        else:
            yield Message(
                role="assistant",
                content="📁 No workspace specified for analysis. Please provide a project path.\n"
            )
    
    def _execute_development_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute development-focused task."""
        # This would integrate with development tools and workflows
        yield Message(
            role="assistant",
            content="🔨 **Development mode activated**\n"
                   "Integrating with development tools and workflows...\n"
        )
        
        # Use standard chat with enhanced context
        yield from self._enhanced_chat_processing(user_input)
    
    def _execute_debugging_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute debugging-focused task."""
        yield Message(
            role="assistant",
            content="🐛 **Debug mode activated**\n"
                   "Analyzing for debugging opportunities...\n"
        )
        
        # Use standard chat with debugging context
        yield from self._enhanced_chat_processing(user_input)
    
    def _execute_autonomous_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute autonomous task with minimal human intervention."""
        yield Message(
            role="assistant",
            content="🤖 **Autonomous mode activated**\n"
                   "Executing task with minimal human intervention...\n"
        )
        
        # Use standard chat with autonomous context
        yield from self._enhanced_chat_processing(user_input)
    
    def _execute_interactive_task(self, user_input: str) -> Generator[Message, None, None]:
        """Execute interactive task with standard processing."""
        yield from self._enhanced_chat_processing(user_input)
    
    def _enhanced_chat_processing(self, user_input: str) -> Generator[Message, None, None]:
        """Enhanced chat processing with context awareness."""
        # Create enhanced system prompt
        system_prompt = self._create_enhanced_system_prompt()
        
        # Initialize tools with recommendations
        tools = init_tools(self.active_context.active_tools or None)
        
        # Create messages
        messages = [create_system_message(system_prompt)]
        messages.extend(self.active_context.conversation_history[-5:])  # Last 5 messages for context
        messages.append(create_user_message(user_input))
        
        # Generate response
        response_generator = generate_response(
            messages=messages,
            model=self.config.llm.model,
            stream=True,
            tools=get_tools_for_llm()
        )
        
        # Process response
        for message in response_generator:
            yield message
    
    def _create_enhanced_system_prompt(self) -> str:
        """Create an enhanced system prompt with context."""
        base_prompt = """You are an enhanced AI coding agent with advanced capabilities including:

- Chain of Thought reasoning and sequential thinking
- Comprehensive research and documentation integration  
- Advanced codebase analysis and understanding
- Multi-file coordination and project-level reasoning
- Self-critique and continuous improvement

Current Context:
"""
        
        context_info = f"""
- Task Complexity: {self.active_context.task_complexity.value}
- Agent Mode: {self.active_context.agent_mode.value}
- Active Tools: {', '.join(self.active_context.active_tools) if self.active_context.active_tools else 'Standard set'}
- Workspace: {self.active_context.workspace_path or 'Not specified'}
- Confidence Level: {self.active_context.confidence_score:.1%}

Use your enhanced capabilities to provide comprehensive, well-reasoned responses.
Apply Chain of Thought reasoning for complex problems.
Leverage research capabilities when additional information is needed.
Perform thorough analysis when working with codebases.
"""
        
        return base_prompt + context_info
    
    def _self_critique_and_improve(self) -> Generator[Message, None, None]:
        """Apply self-critique and improvement mechanisms."""
        if self.active_context.task_complexity in [TaskComplexity.COMPLEX, TaskComplexity.EXPERT]:
            yield Message(
                role="assistant",
                content="🔍 **Self-critique and improvement analysis...**\n"
            )
            
            # Analyze performance and suggest improvements
            critique_points = []
            
            if self.active_context.confidence_score < 0.8:
                critique_points.append("Consider additional research or analysis to improve confidence")
            
            if not self.active_context.active_tools:
                critique_points.append("Could benefit from more targeted tool selection")
            
            if len(self.active_context.thinking_sessions) == 0 and self.active_context.task_complexity == TaskComplexity.EXPERT:
                critique_points.append("Complex task could benefit from Chain of Thought reasoning")
            
            if critique_points:
                yield Message(
                    role="assistant",
                    content="**Areas for improvement:**\n" + 
                           "\n".join(f"• {point}" for point in critique_points) + "\n"
                )
            else:
                yield Message(
                    role="assistant",
                    content="✅ **Performance analysis:** Task executed optimally\n"
                )
    
    def _update_performance_metrics(self) -> None:
        """Update performance metrics."""
        self.performance_metrics["tasks_completed"] += 1
        
        # Update average confidence
        current_avg = self.performance_metrics["average_confidence"]
        task_count = self.performance_metrics["tasks_completed"]
        new_avg = ((current_avg * (task_count - 1)) + self.active_context.confidence_score) / task_count
        self.performance_metrics["average_confidence"] = new_avg
        
        # Update tool usage stats
        for tool in self.active_context.active_tools:
            self.performance_metrics["tool_usage_stats"][tool] = (
                self.performance_metrics["tool_usage_stats"].get(tool, 0) + 1
            )
    
    def _fallback_processing(
        self,
        user_input: str,
        conversation_id: Optional[str],
        workspace_path: Optional[str]
    ) -> Generator[Message, None, None]:
        """Fallback to standard chat processing."""
        try:
            # Use standard chat function
            initial_messages = create_initial_messages()
            initial_messages.append(create_user_message(user_input))
            
            # This would need to be adapted to work as a generator
            yield Message(
                role="assistant",
                content="Using standard processing mode...\n"
            )
            
        except Exception as e:
            yield Message(
                role="assistant",
                content=f"Error in fallback processing: {str(e)}\n"
            )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        return {
            "performance_metrics": self.performance_metrics,
            "current_context": {
                "task_complexity": self.active_context.task_complexity.value,
                "agent_mode": self.active_context.agent_mode.value,
                "confidence_score": self.active_context.confidence_score,
                "active_tools": self.active_context.active_tools,
                "thinking_sessions": len(self.active_context.thinking_sessions)
            }
        }


# Global orchestrator instance
_orchestrator: Optional[EnhancedAgentOrchestrator] = None


def get_enhanced_orchestrator() -> EnhancedAgentOrchestrator:
    """Get the global enhanced agent orchestrator."""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = EnhancedAgentOrchestrator()
    return _orchestrator


__all__ = [
    "EnhancedAgentOrchestrator",
    "AgentMode",
    "TaskComplexity", 
    "AgentContext",
    "get_enhanced_orchestrator",
]
