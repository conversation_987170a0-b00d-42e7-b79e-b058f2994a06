"""
Advanced Code Intelligence System.

Provides superior AI capabilities that surpass competing coding assistants
including advanced code understanding, refactoring, debugging, and suggestions.
"""

import ast
import re
import json
import logging
from typing import Dict, List, Optional, Any, Set, Tuple, Generator
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)


@dataclass
class CodeSuggestion:
    """Represents a code suggestion."""
    type: str  # completion, refactor, fix, optimization
    description: str
    code: str
    confidence: float
    line_number: int
    column: int
    reasoning: str
    impact: str = "low"  # low, medium, high
    category: str = "general"  # general, performance, security, style


@dataclass
class CodeIssue:
    """Represents a code issue or bug."""
    severity: str  # error, warning, info
    message: str
    line_number: int
    column: int
    rule: str
    fix_suggestion: Optional[str] = None
    category: str = "general"


@dataclass
class RefactoringOpportunity:
    """Represents a refactoring opportunity."""
    type: str  # extract_method, rename, move_class, etc.
    description: str
    location: Tuple[int, int]  # start_line, end_line
    benefit: str
    effort: str  # low, medium, high
    code_before: str
    code_after: str
    confidence: float


@dataclass
class ArchitecturalInsight:
    """Represents architectural analysis insight."""
    pattern: str
    description: str
    recommendation: str
    impact: str
    files_involved: List[str]
    confidence: float


class AdvancedCodeIntelligence:
    """
    Advanced code intelligence system with superior capabilities.
    
    Features:
    - Multi-language code understanding
    - Context-aware code completion
    - Intelligent refactoring suggestions
    - Advanced debugging and error detection
    - Performance optimization recommendations
    - Security vulnerability detection
    - Code quality analysis
    - Architectural pattern recognition
    """
    
    def __init__(self, workspace_path: str):
        """Initialize the advanced code intelligence system."""
        self.workspace_path = Path(workspace_path)
        
        # Language-specific analyzers
        self.analyzers = {
            'python': PythonAnalyzer(),
            'javascript': JavaScriptAnalyzer(),
            'typescript': TypeScriptAnalyzer(),
            'java': JavaAnalyzer(),
            'cpp': CppAnalyzer(),
            'go': GoAnalyzer(),
            'rust': RustAnalyzer()
        }
        
        # Pattern recognition
        self.patterns = {
            'design_patterns': DesignPatternDetector(),
            'anti_patterns': AntiPatternDetector(),
            'security_patterns': SecurityPatternDetector(),
            'performance_patterns': PerformancePatternDetector()
        }
        
        # Context tracking
        self.project_context = ProjectContext(workspace_path)
        
        # Learning system
        self.learning_system = LearningSystem()
    
    def analyze_code(self, file_path: str, content: str) -> Dict[str, Any]:
        """
        Perform comprehensive code analysis.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content
        
        Returns:
            Comprehensive analysis results
        """
        language = self._detect_language(file_path)
        analyzer = self.analyzers.get(language)
        
        if not analyzer:
            return {'error': f'Unsupported language: {language}'}
        
        # Perform analysis
        analysis = {
            'file_path': file_path,
            'language': language,
            'suggestions': [],
            'issues': [],
            'refactoring_opportunities': [],
            'architectural_insights': [],
            'metrics': {},
            'dependencies': [],
            'exports': [],
            'complexity': 0
        }
        
        try:
            # Basic analysis
            analysis['metrics'] = analyzer.calculate_metrics(content)
            analysis['complexity'] = analyzer.calculate_complexity(content)
            analysis['dependencies'] = analyzer.extract_dependencies(content)
            analysis['exports'] = analyzer.extract_exports(content)
            
            # Advanced analysis
            analysis['suggestions'] = self._generate_suggestions(content, language, file_path)
            analysis['issues'] = self._detect_issues(content, language)
            analysis['refactoring_opportunities'] = self._find_refactoring_opportunities(content, language)
            analysis['architectural_insights'] = self._analyze_architecture(file_path, content)
            
            # Context-aware enhancements
            self._enhance_with_context(analysis)
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def generate_code_completion(
        self,
        file_path: str,
        content: str,
        cursor_line: int,
        cursor_column: int,
        context_lines: int = 10
    ) -> List[CodeSuggestion]:
        """
        Generate intelligent code completion suggestions.
        
        Args:
            file_path: Path to the file
            content: File content
            cursor_line: Current cursor line
            cursor_column: Current cursor column
            context_lines: Number of context lines to consider
        
        Returns:
            List of code completion suggestions
        """
        language = self._detect_language(file_path)
        analyzer = self.analyzers.get(language)
        
        if not analyzer:
            return []
        
        # Get context around cursor
        lines = content.splitlines()
        start_line = max(0, cursor_line - context_lines)
        end_line = min(len(lines), cursor_line + context_lines)
        context = '\n'.join(lines[start_line:end_line])
        
        # Generate completions
        completions = []
        
        try:
            # Current line analysis
            current_line = lines[cursor_line] if cursor_line < len(lines) else ""
            prefix = current_line[:cursor_column]
            
            # Generate different types of completions
            completions.extend(analyzer.generate_method_completions(context, prefix))
            completions.extend(analyzer.generate_variable_completions(context, prefix))
            completions.extend(analyzer.generate_import_completions(content, prefix))
            completions.extend(self._generate_pattern_completions(context, prefix, language))
            completions.extend(self._generate_context_aware_completions(file_path, context, prefix))
            
            # Sort by confidence
            completions.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            logger.error(f"Error generating completions: {e}")
        
        return completions[:20]  # Return top 20 suggestions
    
    def suggest_refactoring(self, file_path: str, content: str) -> List[RefactoringOpportunity]:
        """
        Suggest refactoring opportunities.
        
        Args:
            file_path: Path to the file
            content: File content
        
        Returns:
            List of refactoring opportunities
        """
        language = self._detect_language(file_path)
        analyzer = self.analyzers.get(language)
        
        if not analyzer:
            return []
        
        opportunities = []
        
        try:
            # Detect various refactoring opportunities
            opportunities.extend(analyzer.detect_long_methods(content))
            opportunities.extend(analyzer.detect_duplicate_code(content))
            opportunities.extend(analyzer.detect_large_classes(content))
            opportunities.extend(analyzer.detect_complex_conditionals(content))
            opportunities.extend(self._detect_cross_cutting_concerns(file_path, content))
            
            # Pattern-based refactoring
            for pattern_detector in self.patterns.values():
                opportunities.extend(pattern_detector.suggest_refactoring(content, language))
            
            # Sort by impact and confidence
            opportunities.sort(key=lambda x: (x.confidence, x.benefit), reverse=True)
            
        except Exception as e:
            logger.error(f"Error suggesting refactoring: {e}")
        
        return opportunities
    
    def detect_bugs_and_issues(self, file_path: str, content: str) -> List[CodeIssue]:
        """
        Detect bugs and code issues.
        
        Args:
            file_path: Path to the file
            content: File content
        
        Returns:
            List of detected issues
        """
        language = self._detect_language(file_path)
        analyzer = self.analyzers.get(language)
        
        if not analyzer:
            return []
        
        issues = []
        
        try:
            # Static analysis
            issues.extend(analyzer.detect_syntax_errors(content))
            issues.extend(analyzer.detect_logical_errors(content))
            issues.extend(analyzer.detect_performance_issues(content))
            issues.extend(analyzer.detect_security_vulnerabilities(content))
            
            # Pattern-based detection
            issues.extend(self.patterns['anti_patterns'].detect_issues(content, language))
            issues.extend(self.patterns['security_patterns'].detect_vulnerabilities(content, language))
            issues.extend(self.patterns['performance_patterns'].detect_issues(content, language))
            
            # Context-aware detection
            issues.extend(self._detect_context_specific_issues(file_path, content))
            
        except Exception as e:
            logger.error(f"Error detecting issues: {e}")
        
        return issues
    
    def analyze_project_architecture(self) -> Dict[str, Any]:
        """
        Analyze the overall project architecture.
        
        Returns:
            Architectural analysis results
        """
        analysis = {
            'patterns': [],
            'anti_patterns': [],
            'recommendations': [],
            'metrics': {},
            'dependencies': {},
            'hotspots': [],
            'technical_debt': []
        }
        
        try:
            # Scan all files
            files_analyzed = 0
            for file_path in self._get_source_files():
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                        content = f.read()
                    
                    file_analysis = self.analyze_code(str(file_path), content)
                    
                    # Aggregate results
                    analysis['patterns'].extend(file_analysis.get('architectural_insights', []))
                    files_analyzed += 1
                    
                except Exception as e:
                    logger.warning(f"Error analyzing {file_path}: {e}")
            
            # Project-level analysis
            analysis['metrics']['files_analyzed'] = files_analyzed
            analysis['dependencies'] = self._analyze_project_dependencies()
            analysis['hotspots'] = self._identify_complexity_hotspots()
            analysis['technical_debt'] = self._assess_technical_debt()
            analysis['recommendations'] = self._generate_architectural_recommendations(analysis)
            
        except Exception as e:
            logger.error(f"Error analyzing project architecture: {e}")
        
        return analysis
    
    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file path."""
        extension = Path(file_path).suffix.lower()
        
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.cc': 'cpp',
            '.cxx': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala'
        }
        
        return language_map.get(extension, 'text')
    
    def _generate_suggestions(self, content: str, language: str, file_path: str) -> List[CodeSuggestion]:
        """Generate code suggestions."""
        suggestions = []
        
        # Performance suggestions
        suggestions.extend(self._suggest_performance_improvements(content, language))
        
        # Style suggestions
        suggestions.extend(self._suggest_style_improvements(content, language))
        
        # Best practice suggestions
        suggestions.extend(self._suggest_best_practices(content, language))
        
        # Context-aware suggestions
        suggestions.extend(self._suggest_context_improvements(file_path, content))
        
        return suggestions
    
    def _detect_issues(self, content: str, language: str) -> List[CodeIssue]:
        """Detect code issues."""
        issues = []
        
        # Common issues across languages
        issues.extend(self._detect_common_issues(content))
        
        # Language-specific issues
        if language == 'python':
            issues.extend(self._detect_python_issues(content))
        elif language in ['javascript', 'typescript']:
            issues.extend(self._detect_js_issues(content))
        
        return issues
    
    def _find_refactoring_opportunities(self, content: str, language: str) -> List[RefactoringOpportunity]:
        """Find refactoring opportunities."""
        opportunities = []
        
        # Extract method opportunities
        opportunities.extend(self._find_extract_method_opportunities(content, language))
        
        # Rename opportunities
        opportunities.extend(self._find_rename_opportunities(content, language))
        
        # Move class opportunities
        opportunities.extend(self._find_move_class_opportunities(content, language))
        
        return opportunities
    
    def _analyze_architecture(self, file_path: str, content: str) -> List[ArchitecturalInsight]:
        """Analyze architectural patterns."""
        insights = []
        
        # Design pattern detection
        insights.extend(self.patterns['design_patterns'].analyze(content, file_path))
        
        # Anti-pattern detection
        insights.extend(self.patterns['anti_patterns'].analyze(content, file_path))
        
        return insights
    
    def _enhance_with_context(self, analysis: Dict[str, Any]):
        """Enhance analysis with project context."""
        # Add project-specific insights
        context = self.project_context.get_context(analysis['file_path'])
        
        # Enhance suggestions with context
        for suggestion in analysis['suggestions']:
            if context:
                suggestion.reasoning += f" (Context: {context.get('framework', 'unknown')})"
    
    def _generate_pattern_completions(self, context: str, prefix: str, language: str) -> List[CodeSuggestion]:
        """Generate pattern-based completions."""
        completions = []
        
        # Common patterns
        if language == 'python':
            if 'class' in context and prefix.strip().endswith('def '):
                completions.append(CodeSuggestion(
                    type="completion",
                    description="Constructor method",
                    code="__init__(self):",
                    confidence=0.9,
                    line_number=0,
                    column=0,
                    reasoning="Common constructor pattern",
                    category="pattern"
                ))
        
        return completions
    
    def _generate_context_aware_completions(self, file_path: str, context: str, prefix: str) -> List[CodeSuggestion]:
        """Generate context-aware completions."""
        completions = []
        
        # Project-specific completions based on existing code
        project_symbols = self.project_context.get_symbols(file_path)
        
        for symbol in project_symbols:
            if symbol.startswith(prefix):
                completions.append(CodeSuggestion(
                    type="completion",
                    description=f"Project symbol: {symbol}",
                    code=symbol,
                    confidence=0.8,
                    line_number=0,
                    column=0,
                    reasoning="Found in project context",
                    category="context"
                ))
        
        return completions
    
    def _suggest_performance_improvements(self, content: str, language: str) -> List[CodeSuggestion]:
        """Suggest performance improvements."""
        suggestions = []
        
        if language == 'python':
            # List comprehension suggestions
            if 'for ' in content and 'append(' in content:
                suggestions.append(CodeSuggestion(
                    type="optimization",
                    description="Consider using list comprehension",
                    code="# Use list comprehension for better performance",
                    confidence=0.7,
                    line_number=0,
                    column=0,
                    reasoning="List comprehensions are generally faster than loops with append",
                    impact="medium",
                    category="performance"
                ))
        
        return suggestions
    
    def _suggest_style_improvements(self, content: str, language: str) -> List[CodeSuggestion]:
        """Suggest style improvements."""
        suggestions = []
        
        # Common style suggestions
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if len(line) > 100:
                suggestions.append(CodeSuggestion(
                    type="style",
                    description="Line too long",
                    code="# Consider breaking this line",
                    confidence=0.6,
                    line_number=i + 1,
                    column=100,
                    reasoning="Lines should be under 100 characters for readability",
                    category="style"
                ))
        
        return suggestions
    
    def _suggest_best_practices(self, content: str, language: str) -> List[CodeSuggestion]:
        """Suggest best practices."""
        suggestions = []
        
        if language == 'python':
            # Docstring suggestions
            if 'def ' in content and '"""' not in content:
                suggestions.append(CodeSuggestion(
                    type="best_practice",
                    description="Add docstring to function",
                    code='"""Function description."""',
                    confidence=0.8,
                    line_number=0,
                    column=0,
                    reasoning="Functions should have docstrings for documentation",
                    category="documentation"
                ))
        
        return suggestions
    
    def _suggest_context_improvements(self, file_path: str, content: str) -> List[CodeSuggestion]:
        """Suggest context-specific improvements."""
        suggestions = []
        
        # File-specific suggestions based on project context
        context = self.project_context.get_context(file_path)
        
        if context and context.get('framework') == 'django':
            if 'models.py' in file_path and 'class ' in content:
                suggestions.append(CodeSuggestion(
                    type="framework",
                    description="Consider adding Meta class",
                    code="class Meta:\n    ordering = ['-created_at']",
                    confidence=0.6,
                    line_number=0,
                    column=0,
                    reasoning="Django models often benefit from Meta class configuration",
                    category="framework"
                ))
        
        return suggestions
    
    def _detect_common_issues(self, content: str) -> List[CodeIssue]:
        """Detect common issues across languages."""
        issues = []
        
        # TODO comments
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if 'TODO' in line.upper():
                issues.append(CodeIssue(
                    severity="info",
                    message="TODO comment found",
                    line_number=i + 1,
                    column=line.upper().find('TODO'),
                    rule="todo_comment",
                    category="maintenance"
                ))
        
        return issues
    
    def _detect_python_issues(self, content: str) -> List[CodeIssue]:
        """Detect Python-specific issues."""
        issues = []
        
        try:
            # Parse AST for syntax errors
            ast.parse(content)
        except SyntaxError as e:
            issues.append(CodeIssue(
                severity="error",
                message=f"Syntax error: {e.msg}",
                line_number=e.lineno or 1,
                column=e.offset or 1,
                rule="syntax_error",
                category="syntax"
            ))
        
        return issues
    
    def _detect_js_issues(self, content: str) -> List[CodeIssue]:
        """Detect JavaScript-specific issues."""
        issues = []
        
        # Simple regex-based checks
        if 'var ' in content:
            lines = content.splitlines()
            for i, line in enumerate(lines):
                if 'var ' in line:
                    issues.append(CodeIssue(
                        severity="warning",
                        message="Consider using 'let' or 'const' instead of 'var'",
                        line_number=i + 1,
                        column=line.find('var '),
                        rule="no_var",
                        fix_suggestion="Replace 'var' with 'let' or 'const'",
                        category="best_practice"
                    ))
        
        return issues
    
    def _find_extract_method_opportunities(self, content: str, language: str) -> List[RefactoringOpportunity]:
        """Find extract method opportunities."""
        opportunities = []
        
        # Simple heuristic: long methods
        if language == 'python':
            lines = content.splitlines()
            in_function = False
            function_start = 0
            function_lines = 0
            
            for i, line in enumerate(lines):
                if line.strip().startswith('def '):
                    if in_function and function_lines > 20:
                        opportunities.append(RefactoringOpportunity(
                            type="extract_method",
                            description="Long method detected",
                            location=(function_start, i),
                            benefit="Improved readability and maintainability",
                            effort="medium",
                            code_before="# Long method",
                            code_after="# Extracted methods",
                            confidence=0.7
                        ))
                    
                    in_function = True
                    function_start = i
                    function_lines = 0
                elif in_function and line.strip():
                    function_lines += 1
        
        return opportunities
    
    def _find_rename_opportunities(self, content: str, language: str) -> List[RefactoringOpportunity]:
        """Find rename opportunities."""
        opportunities = []
        
        # Simple heuristic: single letter variables
        if language == 'python':
            import re
            single_letter_vars = re.findall(r'\b[a-z]\b', content)
            if single_letter_vars:
                opportunities.append(RefactoringOpportunity(
                    type="rename",
                    description="Single letter variables found",
                    location=(0, 0),
                    benefit="Improved code readability",
                    effort="low",
                    code_before="# Single letter variables",
                    code_after="# Descriptive variable names",
                    confidence=0.6
                ))
        
        return opportunities
    
    def _find_move_class_opportunities(self, content: str, language: str) -> List[RefactoringOpportunity]:
        """Find move class opportunities."""
        opportunities = []
        
        # This would be more sophisticated in a real implementation
        # For now, just a placeholder
        
        return opportunities
    
    def _detect_cross_cutting_concerns(self, file_path: str, content: str) -> List[RefactoringOpportunity]:
        """Detect cross-cutting concerns that could be refactored."""
        opportunities = []
        
        # Logging patterns
        if 'logging' in content or 'log.' in content:
            opportunities.append(RefactoringOpportunity(
                type="extract_aspect",
                description="Logging cross-cutting concern detected",
                location=(0, 0),
                benefit="Centralized logging management",
                effort="medium",
                code_before="# Scattered logging calls",
                code_after="# Centralized logging aspect",
                confidence=0.5
            ))
        
        return opportunities
    
    def _get_source_files(self) -> List[Path]:
        """Get all source files in the project."""
        source_files = []
        
        for ext in ['.py', '.js', '.ts', '.java', '.cpp', '.go', '.rs']:
            source_files.extend(self.workspace_path.rglob(f'*{ext}'))
        
        return source_files
    
    def _analyze_project_dependencies(self) -> Dict[str, Any]:
        """Analyze project dependencies."""
        dependencies = {
            'internal': [],
            'external': [],
            'circular': [],
            'unused': []
        }
        
        # This would be a more sophisticated analysis
        # For now, just a placeholder
        
        return dependencies
    
    def _identify_complexity_hotspots(self) -> List[str]:
        """Identify complexity hotspots in the project."""
        hotspots = []
        
        # This would analyze complexity metrics across files
        # For now, just a placeholder
        
        return hotspots
    
    def _assess_technical_debt(self) -> List[Dict[str, Any]]:
        """Assess technical debt in the project."""
        debt_items = []
        
        # This would analyze various debt indicators
        # For now, just a placeholder
        
        return debt_items
    
    def _generate_architectural_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate architectural recommendations."""
        recommendations = []
        
        # Based on the analysis, generate recommendations
        if analysis['metrics']['files_analyzed'] > 100:
            recommendations.append("Consider modularizing the large codebase")
        
        return recommendations


# Placeholder classes for language-specific analyzers
class PythonAnalyzer:
    def calculate_metrics(self, content: str) -> Dict[str, Any]:
        return {'lines': len(content.splitlines())}
    
    def calculate_complexity(self, content: str) -> int:
        return content.count('if') + content.count('for') + content.count('while')
    
    def extract_dependencies(self, content: str) -> List[str]:
        import re
        imports = re.findall(r'import\s+(\w+)', content)
        from_imports = re.findall(r'from\s+(\w+)\s+import', content)
        return imports + from_imports
    
    def extract_exports(self, content: str) -> List[str]:
        import re
        return re.findall(r'def\s+(\w+)', content)
    
    def generate_method_completions(self, context: str, prefix: str) -> List[CodeSuggestion]:
        return []
    
    def generate_variable_completions(self, context: str, prefix: str) -> List[CodeSuggestion]:
        return []
    
    def generate_import_completions(self, content: str, prefix: str) -> List[CodeSuggestion]:
        return []
    
    def detect_long_methods(self, content: str) -> List[RefactoringOpportunity]:
        return []
    
    def detect_duplicate_code(self, content: str) -> List[RefactoringOpportunity]:
        return []
    
    def detect_large_classes(self, content: str) -> List[RefactoringOpportunity]:
        return []
    
    def detect_complex_conditionals(self, content: str) -> List[RefactoringOpportunity]:
        return []
    
    def detect_syntax_errors(self, content: str) -> List[CodeIssue]:
        return []
    
    def detect_logical_errors(self, content: str) -> List[CodeIssue]:
        return []
    
    def detect_performance_issues(self, content: str) -> List[CodeIssue]:
        return []
    
    def detect_security_vulnerabilities(self, content: str) -> List[CodeIssue]:
        return []


# Placeholder classes for other analyzers
class JavaScriptAnalyzer(PythonAnalyzer):
    pass

class TypeScriptAnalyzer(PythonAnalyzer):
    pass

class JavaAnalyzer(PythonAnalyzer):
    pass

class CppAnalyzer(PythonAnalyzer):
    pass

class GoAnalyzer(PythonAnalyzer):
    pass

class RustAnalyzer(PythonAnalyzer):
    pass


# Placeholder classes for pattern detectors
class DesignPatternDetector:
    def analyze(self, content: str, file_path: str) -> List[ArchitecturalInsight]:
        return []
    
    def suggest_refactoring(self, content: str, language: str) -> List[RefactoringOpportunity]:
        return []


class AntiPatternDetector:
    def analyze(self, content: str, file_path: str) -> List[ArchitecturalInsight]:
        return []
    
    def detect_issues(self, content: str, language: str) -> List[CodeIssue]:
        return []
    
    def suggest_refactoring(self, content: str, language: str) -> List[RefactoringOpportunity]:
        return []


class SecurityPatternDetector:
    def detect_vulnerabilities(self, content: str, language: str) -> List[CodeIssue]:
        return []
    
    def suggest_refactoring(self, content: str, language: str) -> List[RefactoringOpportunity]:
        return []


class PerformancePatternDetector:
    def detect_issues(self, content: str, language: str) -> List[CodeIssue]:
        return []
    
    def suggest_refactoring(self, content: str, language: str) -> List[RefactoringOpportunity]:
        return []


class ProjectContext:
    def __init__(self, workspace_path: str):
        self.workspace_path = workspace_path
    
    def get_context(self, file_path: str) -> Dict[str, Any]:
        return {'framework': 'unknown'}
    
    def get_symbols(self, file_path: str) -> List[str]:
        return []


class LearningSystem:
    def __init__(self):
        pass


def create_advanced_code_intelligence(workspace_path: str) -> AdvancedCodeIntelligence:
    """
    Create and return an advanced code intelligence system.
    
    Args:
        workspace_path: Path to the workspace
    
    Returns:
        AdvancedCodeIntelligence instance
    """
    return AdvancedCodeIntelligence(workspace_path)


if __name__ == "__main__":
    # Demo mode
    intelligence = create_advanced_code_intelligence(".")
    
    demo_code = '''
def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total
'''
    
    analysis = intelligence.analyze_code("demo.py", demo_code)
    print(f"Analysis complete: {len(analysis['suggestions'])} suggestions found")
