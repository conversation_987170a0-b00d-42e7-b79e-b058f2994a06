"""
Enhanced Tool Discovery and Integration System.

This module provides intelligent tool discovery, context-aware recommendations,
and comprehensive tool management capabilities for the AI agent.
"""

import logging
import json
import threading
from typing import Dict, List, Optional, Any, Callable, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from tools.base import ToolSpec, Parameter
from tools.registry import ToolRegistry
from message import Message
from config import get_config

logger = logging.getLogger(__name__)


class ToolCategory(Enum):
    """Categories for tool classification."""
    CODE_ANALYSIS = "code_analysis"
    FILE_OPERATIONS = "file_operations"
    WEB_RESEARCH = "web_research"
    SYSTEM_INTERACTION = "system_interaction"
    DEVELOPMENT = "development"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    COMMUNICATION = "communication"
    DATA_PROCESSING = "data_processing"
    REASONING = "reasoning"


class ToolContext(Enum):
    """Context types for tool recommendations."""
    DEBUGGING = "debugging"
    FEATURE_DEVELOPMENT = "feature_development"
    REFACTORING = "refactoring"
    TESTING = "testing"
    DOCUMENTATION = "documentation"
    RESEARCH = "research"
    DEPLOYMENT = "deployment"
    MAINTENANCE = "maintenance"


@dataclass
class ToolMetadata:
    """Enhanced metadata for tools."""
    category: ToolCategory
    contexts: List[ToolContext] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    outputs: List[str] = field(default_factory=list)
    confidence_score: float = 1.0
    usage_frequency: int = 0
    success_rate: float = 1.0
    average_execution_time: float = 0.0
    dependencies: List[str] = field(default_factory=list)
    tags: Set[str] = field(default_factory=set)
    examples: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ToolRecommendation:
    """Tool recommendation with reasoning."""
    tool_name: str
    confidence: float
    reasoning: str
    context_match: float
    usage_pattern: str
    suggested_parameters: Dict[str, Any] = field(default_factory=dict)


class EnhancedToolDiscovery:
    """
    Enhanced tool discovery system with intelligent recommendations.
    
    Features:
    - Context-aware tool suggestions
    - Usage pattern analysis
    - Tool performance tracking
    - Intelligent parameter suggestions
    - Tool chaining recommendations
    """
    
    def __init__(self):
        """Initialize the enhanced tool discovery system."""
        self.tool_metadata: Dict[str, ToolMetadata] = {}
        self.usage_history: List[Dict[str, Any]] = []
        self.context_patterns: Dict[str, List[str]] = {}
        self.tool_chains: Dict[str, List[str]] = {}
        self._lock = threading.Lock()
        
        # Load existing metadata and patterns
        self._load_metadata()
        self._initialize_default_patterns()
    
    def register_tool_metadata(
        self,
        tool_name: str,
        metadata: ToolMetadata
    ) -> None:
        """Register metadata for a tool."""
        with self._lock:
            self.tool_metadata[tool_name] = metadata
            logger.debug(f"Registered metadata for tool: {tool_name}")
    
    def recommend_tools(
        self,
        context: str,
        current_task: Optional[str] = None,
        conversation_history: Optional[List[Message]] = None,
        max_recommendations: int = 5
    ) -> List[ToolRecommendation]:
        """
        Recommend tools based on context and history.
        
        Args:
            context: Current context description
            current_task: Current task description
            conversation_history: Recent conversation messages
            max_recommendations: Maximum number of recommendations
        
        Returns:
            List of tool recommendations
        """
        recommendations = []
        
        # Analyze context for tool categories
        context_categories = self._analyze_context(context, current_task)
        
        # Get conversation context
        conv_context = self._analyze_conversation(conversation_history or [])
        
        # Score all available tools
        for tool_name, metadata in self.tool_metadata.items():
            score = self._calculate_tool_score(
                tool_name,
                metadata,
                context_categories,
                conv_context,
                context
            )
            
            if score > 0.3:  # Minimum threshold
                reasoning = self._generate_reasoning(
                    tool_name,
                    metadata,
                    context_categories,
                    score
                )
                
                recommendations.append(ToolRecommendation(
                    tool_name=tool_name,
                    confidence=score,
                    reasoning=reasoning,
                    context_match=self._calculate_context_match(metadata, context_categories),
                    usage_pattern=self._get_usage_pattern(tool_name),
                    suggested_parameters=self._suggest_parameters(tool_name, context)
                ))
        
        # Sort by confidence and return top recommendations
        recommendations.sort(key=lambda x: x.confidence, reverse=True)
        return recommendations[:max_recommendations]
    
    def get_tool_chains(
        self,
        starting_tool: str,
        goal: str
    ) -> List[List[str]]:
        """
        Suggest tool chains for achieving a goal.
        
        Args:
            starting_tool: Initial tool to start with
            goal: Desired outcome
        
        Returns:
            List of tool chains (sequences)
        """
        chains = []
        
        # Look for existing patterns
        if starting_tool in self.tool_chains:
            chains.extend([
                [starting_tool] + chain 
                for chain in self.tool_chains[starting_tool]
            ])
        
        # Generate new chains based on goal analysis
        goal_chains = self._generate_goal_chains(starting_tool, goal)
        chains.extend(goal_chains)
        
        return chains[:3]  # Return top 3 chains
    
    def record_tool_usage(
        self,
        tool_name: str,
        success: bool,
        execution_time: float,
        context: str,
        parameters: Dict[str, Any]
    ) -> None:
        """Record tool usage for learning."""
        with self._lock:
            # Update metadata
            if tool_name in self.tool_metadata:
                metadata = self.tool_metadata[tool_name]
                metadata.usage_frequency += 1
                
                # Update success rate
                total_uses = metadata.usage_frequency
                current_successes = metadata.success_rate * (total_uses - 1)
                new_successes = current_successes + (1 if success else 0)
                metadata.success_rate = new_successes / total_uses
                
                # Update average execution time
                current_avg = metadata.average_execution_time
                metadata.average_execution_time = (
                    (current_avg * (total_uses - 1) + execution_time) / total_uses
                )
            
            # Record usage history
            self.usage_history.append({
                "tool_name": tool_name,
                "success": success,
                "execution_time": execution_time,
                "context": context,
                "parameters": parameters,
                "timestamp": time.time()
            })
            
            # Keep only recent history
            if len(self.usage_history) > 1000:
                self.usage_history = self.usage_history[-1000:]
    
    def _analyze_context(
        self,
        context: str,
        task: Optional[str] = None
    ) -> List[ToolCategory]:
        """Analyze context to determine relevant tool categories."""
        categories = []
        context_text = f"{context} {task or ''}".lower()
        
        # Keyword-based category detection
        category_keywords = {
            ToolCategory.CODE_ANALYSIS: ["analyze", "review", "inspect", "examine", "code"],
            ToolCategory.FILE_OPERATIONS: ["file", "read", "write", "create", "delete", "modify"],
            ToolCategory.WEB_RESEARCH: ["search", "research", "web", "documentation", "lookup"],
            ToolCategory.SYSTEM_INTERACTION: ["command", "shell", "system", "execute", "run"],
            ToolCategory.DEVELOPMENT: ["develop", "build", "implement", "create", "code"],
            ToolCategory.TESTING: ["test", "verify", "validate", "check", "debug"],
            ToolCategory.DOCUMENTATION: ["document", "readme", "docs", "explain", "describe"],
            ToolCategory.REASONING: ["think", "analyze", "reason", "plan", "strategy"]
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in context_text for keyword in keywords):
                categories.append(category)
        
        return categories or [ToolCategory.DEVELOPMENT]  # Default category
    
    def _analyze_conversation(self, messages: List[Message]) -> Dict[str, Any]:
        """Analyze conversation history for context."""
        context = {
            "recent_tools": [],
            "error_patterns": [],
            "success_patterns": [],
            "topics": set()
        }
        
        # Analyze recent messages
        for msg in messages[-10:]:  # Last 10 messages
            if hasattr(msg, 'tool_calls') and msg.tool_calls:
                context["recent_tools"].extend(msg.tool_calls)
            
            # Extract topics and patterns
            content = msg.content.lower()
            if "error" in content or "failed" in content:
                context["error_patterns"].append(content)
            elif "success" in content or "completed" in content:
                context["success_patterns"].append(content)
        
        return context
    
    def _calculate_tool_score(
        self,
        tool_name: str,
        metadata: ToolMetadata,
        context_categories: List[ToolCategory],
        conv_context: Dict[str, Any],
        context: str
    ) -> float:
        """Calculate relevance score for a tool."""
        score = 0.0
        
        # Category match score (40%)
        if metadata.category in context_categories:
            score += 0.4
        
        # Context match score (30%)
        context_match = sum(
            1 for ctx in metadata.contexts
            if any(keyword in context.lower() for keyword in [ctx.value])
        ) / max(len(metadata.contexts), 1)
        score += 0.3 * context_match
        
        # Performance score (20%)
        performance = (metadata.success_rate * 0.7 + 
                      (1.0 - min(metadata.average_execution_time / 60.0, 1.0)) * 0.3)
        score += 0.2 * performance
        
        # Usage frequency score (10%)
        max_frequency = max(
            (m.usage_frequency for m in self.tool_metadata.values()),
            default=1
        )
        frequency_score = metadata.usage_frequency / max_frequency
        score += 0.1 * frequency_score
        
        return min(score, 1.0)
    
    def _calculate_context_match(
        self,
        metadata: ToolMetadata,
        categories: List[ToolCategory]
    ) -> float:
        """Calculate how well tool matches the context."""
        if not categories:
            return 0.5
        
        matches = sum(1 for cat in categories if cat == metadata.category)
        return matches / len(categories)
    
    def _generate_reasoning(
        self,
        tool_name: str,
        metadata: ToolMetadata,
        categories: List[ToolCategory],
        score: float
    ) -> str:
        """Generate human-readable reasoning for recommendation."""
        reasons = []
        
        if metadata.category in categories:
            reasons.append(f"matches {metadata.category.value} context")
        
        if metadata.success_rate > 0.8:
            reasons.append(f"high success rate ({metadata.success_rate:.1%})")
        
        if metadata.usage_frequency > 10:
            reasons.append("frequently used")
        
        if metadata.average_execution_time < 5.0:
            reasons.append("fast execution")
        
        return f"Recommended because it {', '.join(reasons)}"
    
    def _get_usage_pattern(self, tool_name: str) -> str:
        """Get usage pattern description for a tool."""
        if tool_name not in self.tool_metadata:
            return "unknown"
        
        metadata = self.tool_metadata[tool_name]
        
        if metadata.usage_frequency > 50:
            return "frequently_used"
        elif metadata.usage_frequency > 10:
            return "regularly_used"
        elif metadata.usage_frequency > 0:
            return "occasionally_used"
        else:
            return "rarely_used"
    
    def _suggest_parameters(
        self,
        tool_name: str,
        context: str
    ) -> Dict[str, Any]:
        """Suggest parameters for a tool based on context."""
        # This would analyze context and suggest appropriate parameters
        # For now, return empty dict - can be enhanced with ML models
        return {}
    
    def _generate_goal_chains(
        self,
        starting_tool: str,
        goal: str
    ) -> List[List[str]]:
        """Generate tool chains to achieve a goal."""
        # Simplified goal-based chain generation
        goal_lower = goal.lower()
        chains = []
        
        if "test" in goal_lower:
            chains.append([starting_tool, "python", "shell"])
        elif "deploy" in goal_lower:
            chains.append([starting_tool, "shell", "github_tool"])
        elif "document" in goal_lower:
            chains.append([starting_tool, "file_ops", "web_tools"])
        
        return chains
    
    def _load_metadata(self) -> None:
        """Load existing tool metadata from storage."""
        try:
            config = get_config()
            metadata_file = Path(config.data_dir) / "tool_metadata.json"
            
            if metadata_file.exists():
                with open(metadata_file, 'r') as f:
                    data = json.load(f)
                    # Convert loaded data back to ToolMetadata objects
                    # Implementation would depend on serialization format
                    pass
        except Exception as e:
            logger.warning(f"Could not load tool metadata: {e}")
    
    def _initialize_default_patterns(self) -> None:
        """Initialize default tool usage patterns."""
        # Default tool chains
        self.tool_chains = {
            "file_ops": [["python"], ["shell", "github_tool"]],
            "python": [["shell"], ["file_ops", "shell"]],
            "web_tools": [["file_ops"], ["rag", "memory_tool"]],
            "shell": [["python"], ["github_tool"]],
        }
        
        # Default context patterns
        self.context_patterns = {
            "debugging": ["python", "shell", "file_ops"],
            "development": ["file_ops", "python", "shell", "github_tool"],
            "research": ["web_tools", "rag", "memory_tool"],
            "testing": ["python", "shell", "file_ops"],
        }


# Global instance
_enhanced_discovery: Optional[EnhancedToolDiscovery] = None


def get_enhanced_discovery() -> EnhancedToolDiscovery:
    """Get the global enhanced tool discovery instance."""
    global _enhanced_discovery
    if _enhanced_discovery is None:
        _enhanced_discovery = EnhancedToolDiscovery()
    return _enhanced_discovery


__all__ = [
    "EnhancedToolDiscovery",
    "ToolCategory",
    "ToolContext", 
    "ToolMetadata",
    "ToolRecommendation",
    "get_enhanced_discovery",
]
