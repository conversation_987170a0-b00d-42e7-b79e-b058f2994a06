"""
Advanced chat system with workflow integration.

This module provides intelligent conversation management with workflow tracking,
context awareness, predictive suggestions, and step-by-step execution capabilities.
"""

import logging
import os
import sys
from typing import List, Optional, Generator, Callable
from pathlib import Path

from message import Message, create_user_message, create_system_message
from llm import reply, get_provider
from tools import init_tools, execute_msg, get_tools_for_llm, ToolFormat
from config import get_config
from conversation import ConversationManager
from workflow import get_workflow_engine, WorkflowStep, WorkflowPriority
from utils.prompt import get_user_input, create_system_prompt
from utils.console import console, print_message
from utils.interrupt import handle_interrupt, set_interruptible, clear_interruptible

# Import enhanced capabilities
try:
    from enhanced_agent_orchestrator import get_enhanced_orchestrator
    ENHANCED_MODE_AVAILABLE = True
except ImportError:
    ENHANCED_MODE_AVAILABLE = False
    logger.warning("Enhanced agent orchestrator not available - using standard mode")

logger = logging.getLogger(__name__)

# Type alias for confirmation function
ConfirmFunc = Callable[[str], bool]


def chat(
    initial_messages: List[Message],
    workspace: Optional[Path] = None,
    model: Optional[str] = None,
    stream: bool = True,
    interactive: bool = True,
    auto_confirm: bool = False,
    tool_allowlist: Optional[List[str]] = None,
    conversation_id: Optional[str] = None,
    enhanced_mode: bool = True
) -> None:
    """
    Main chat function for interactive AI agent sessions.

    Args:
        initial_messages: Initial conversation messages
        workspace: Working directory path
        model: LLM model to use
        stream: Whether to stream responses
        interactive: Whether to run in interactive mode
        auto_confirm: Whether to auto-confirm tool executions
        tool_allowlist: List of allowed tools
        conversation_id: Conversation ID for persistence
        enhanced_mode: Whether to use enhanced agent capabilities
    """
    config = get_config()

    # Check if enhanced mode is requested and available
    use_enhanced = enhanced_mode and ENHANCED_MODE_AVAILABLE

    if use_enhanced:
        console.print("🚀 [bold green]Enhanced AI Agent Mode Activated[/bold green]")
        console.print("Features: Chain of Thought reasoning, Advanced research, Multi-file coordination")
    else:
        if enhanced_mode and not ENHANCED_MODE_AVAILABLE:
            console.print("⚠️ [yellow]Enhanced mode requested but not available - using standard mode[/yellow]")
    
    # Set workspace
    if workspace:
        os.chdir(workspace)
        console.print(f"Working directory: {workspace}")
    
    # Initialize tools
    tools = init_tools(tool_allowlist)
    console.print(f"Initialized {len(tools)} tools: {[t.name for t in tools]}")
    
    # Initialize conversation manager
    conversation = ConversationManager(conversation_id)
    
    # Add initial messages
    for msg in initial_messages:
        conversation.add_message(msg)
    
    # Create confirmation function
    def confirm_func(message: str) -> bool:
        if auto_confirm:
            return True
        
        response = input(f"\n{message} (y/N): ").strip().lower()
        return response in ['y', 'yes']
    
    # Print conversation history
    if conversation.messages:
        console.print("\n--- Conversation History ---")
        for msg in conversation.messages:
            print_message(msg)
        console.print("--- End History ---\n")
    
    # Main chat loop
    try:
        while True:
            try:
                # Get user input (if interactive)
                if interactive:
                    user_input = get_user_input()
                    if user_input is None:  # User wants to exit
                        break
                    
                    # Handle special commands
                    if user_input.startswith('/'):
                        if handle_command(user_input, conversation):
                            continue
                        else:
                            break
                    
                    # Add user message
                    user_msg = create_user_message(user_input)
                    conversation.add_message(user_msg)
                    print_message(user_msg)

                # Generate AI response - use enhanced mode if available
                if use_enhanced and user_input and not user_input.startswith('/'):
                    # Use enhanced agent orchestrator
                    orchestrator = get_enhanced_orchestrator()
                    response_generator = orchestrator.process_user_request(
                        user_input=user_input,
                        conversation_id=conversation_id,
                        workspace_path=str(workspace) if workspace else None
                    )
                else:
                    # Use standard response generation
                    response_generator = generate_response(
                        conversation.messages,
                        model=model,
                        stream=stream,
                        tools=get_tools_for_llm()
                    )
                
                # Process response
                assistant_msg = None
                for msg_chunk in response_generator:
                    if stream:
                        print_message(msg_chunk, end="")
                    else:
                        assistant_msg = msg_chunk
                
                if stream:
                    # Combine streamed chunks into final message
                    assistant_msg = create_system_message("Response completed")
                
                if assistant_msg:
                    conversation.add_message(assistant_msg)
                    if not stream:
                        print_message(assistant_msg)
                
                # Execute any tools in the response
                if assistant_msg:
                    tool_responses = list(execute_msg(assistant_msg, confirm_func))
                    for tool_response in tool_responses:
                        conversation.add_message(tool_response)
                        print_message(tool_response)
                
                # Save conversation
                conversation.save()
                
                # Exit if not interactive
                if not interactive:
                    break
                    
            except KeyboardInterrupt:
                console.print("\n[yellow]Interrupted by user[/yellow]")
                if not interactive:
                    break
                continue
            except Exception as e:
                logger.error(f"Error in chat loop: {e}")
                console.print(f"[red]Error: {e}[/red]")
                if not interactive:
                    break
                continue
    
    finally:
        # Save final conversation state
        conversation.save()
        console.print("\n[green]Conversation saved[/green]")


def generate_response(
    messages: List[Message],
    model: Optional[str] = None,
    stream: bool = True,
    tools: Optional[List[dict]] = None
) -> Generator[Message, None, None]:
    """
    Generate AI response for given messages.
    
    Args:
        messages: Conversation messages
        model: Model to use
        stream: Whether to stream response
        tools: Available tools
    
    Yields:
        Response message chunks
    """
    try:
        set_interruptible()
        
        # Generate response using LLM
        response = reply(
            messages=messages,
            model=model,
            stream=stream,
            tools=tools
        )
        
        if stream and hasattr(response, '__iter__'):
            # Stream response
            accumulated_content = ""
            for chunk in response:
                accumulated_content += chunk.content
                yield chunk
            
            # Yield final complete message
            yield Message(
                role="assistant",
                content=accumulated_content,
                metadata={"final": True}
            )
        else:
            # Single response
            yield response
            
    except KeyboardInterrupt:
        yield create_system_message("Response generation interrupted")
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        yield create_system_message(f"Error generating response: {e}")
    finally:
        clear_interruptible()


def handle_command(command: str, conversation: ConversationManager) -> bool:
    """
    Handle special chat commands.
    
    Args:
        command: Command string starting with '/'
        conversation: Conversation manager
    
    Returns:
        True to continue chat, False to exit
    """
    command = command.lower().strip()
    
    if command in ['/exit', '/quit', '/q']:
        return False
    
    elif command == '/help':
        console.print("""
Available commands:
  /help     - Show this help message
  /exit     - Exit the chat
  /clear    - Clear conversation history
  /save     - Save conversation
  /load     - Load conversation
  /tools    - List available tools
  /model    - Show current model info
  /history  - Show conversation history
        """)
    
    elif command == '/clear':
        conversation.clear()
        console.print("[yellow]Conversation history cleared[/yellow]")
    
    elif command == '/save':
        conversation.save()
        console.print("[green]Conversation saved[/green]")
    
    elif command == '/tools':
        tools = init_tools()
        console.print(f"Available tools ({len(tools)}):")
        for tool in tools:
            status = "✓" if tool.is_available() else "✗"
            console.print(f"  {status} {tool.name}: {tool.description}")
    
    elif command == '/model':
        provider = get_provider()
        console.print(f"Current model: {provider.model}")
    
    elif command == '/history':
        console.print("\n--- Conversation History ---")
        for i, msg in enumerate(conversation.messages):
            console.print(f"{i+1}. {msg.role}: {msg.content[:100]}...")
        console.print("--- End History ---\n")
    
    else:
        console.print(f"[red]Unknown command: {command}[/red]")
    
    return True


def create_initial_messages(
    system_prompt: Optional[str] = None,
    tools: Optional[List[str]] = None
) -> List[Message]:
    """
    Create initial system messages for a conversation.
    
    Args:
        system_prompt: Custom system prompt
        tools: List of available tools
    
    Returns:
        List of initial messages
    """
    messages = []
    
    # Create system prompt
    if system_prompt is None:
        system_prompt = create_system_prompt(tools or [])
    
    messages.append(create_system_message(system_prompt))
    
    return messages


__all__ = [
    "chat",
    "generate_response", 
    "handle_command",
    "create_initial_messages",
    "ConfirmFunc",
]
