"""
Code Editor Component for Modern Terminal UI.

Provides a code editor with syntax highlighting, line numbers, and editing capabilities.
"""

import os
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

try:
    from rich.console import Console
    from rich.text import Text
    from rich.panel import Panel
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.columns import Columns
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

from .syntax_highlighter import SyntaxHighlighter


class CodeEditor:
    """
    Modern code editor with syntax highlighting and editing capabilities.
    
    Features:
    - Syntax highlighting for multiple languages
    - Line numbers
    - Code folding indicators
    - Error highlighting
    - Search and replace
    - Multiple file support
    """
    
    def __init__(self):
        """Initialize the code editor."""
        self.current_file: Optional[str] = None
        self.content: List[str] = []
        self.cursor_line = 0
        self.cursor_column = 0
        self.scroll_offset = 0
        self.modified = False
        
        # Editor settings
        self.show_line_numbers = True
        self.tab_size = 4
        self.wrap_lines = False
        self.highlight_current_line = True
        
        # Syntax highlighter
        self.syntax_highlighter = SyntaxHighlighter()
        
        # Search state
        self.search_query = ""
        self.search_results: List[Tuple[int, int]] = []  # (line, column) pairs
        self.current_search_index = -1
    
    def load_file(self, file_path: str) -> bool:
        """Load a file into the editor."""
        try:
            path = Path(file_path)
            if not path.exists():
                self.content = [""]
                self.current_file = file_path
                self.modified = False
                return True
            
            with open(path, 'r', encoding='utf-8', errors='replace') as f:
                self.content = f.read().splitlines()
            
            # Ensure at least one empty line
            if not self.content:
                self.content = [""]
            
            self.current_file = file_path
            self.cursor_line = 0
            self.cursor_column = 0
            self.scroll_offset = 0
            self.modified = False
            
            return True
        
        except Exception as e:
            self.content = [f"Error loading file: {str(e)}"]
            return False
    
    def save_file(self, file_path: Optional[str] = None) -> bool:
        """Save the current content to a file."""
        try:
            target_path = file_path or self.current_file
            if not target_path:
                return False
            
            path = Path(target_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.content))
            
            self.current_file = target_path
            self.modified = False
            return True
        
        except Exception:
            return False
    
    def get_display(self, file_path: Optional[str] = None, max_lines: int = 20) -> Any:
        """Get the rich display for the code editor."""
        if file_path and file_path != self.current_file:
            self.load_file(file_path)
        
        if not RICH_AVAILABLE:
            return self._get_plain_display(max_lines)
        
        if not self.content:
            return Text("No file loaded", style="dim italic")
        
        # Get language for syntax highlighting
        language = self._detect_language()
        
        # Create content to display
        start_line = max(0, self.scroll_offset)
        end_line = min(len(self.content), start_line + max_lines)
        
        display_text = Text()
        
        for i in range(start_line, end_line):
            line_content = self.content[i] if i < len(self.content) else ""
            
            # Add line number
            if self.show_line_numbers:
                line_num = f"{i + 1:4d} "
                if i == self.cursor_line:
                    display_text.append(line_num, style="bold yellow")
                else:
                    display_text.append(line_num, style="dim")
                display_text.append("│ ", style="dim")
            
            # Add line content with syntax highlighting
            if language and line_content.strip():
                highlighted_line = self.syntax_highlighter.highlight_line(line_content, language)
                display_text.append(highlighted_line)
            else:
                # Highlight current line
                if i == self.cursor_line and self.highlight_current_line:
                    display_text.append(line_content, style="on dark_blue")
                else:
                    display_text.append(line_content)
            
            # Add cursor indicator
            if i == self.cursor_line and self.cursor_column <= len(line_content):
                # This is simplified - in a real editor, you'd position the cursor properly
                pass
            
            if i < end_line - 1:
                display_text.append("\n")
        
        return display_text
    
    def _get_plain_display(self, max_lines: int = 20) -> str:
        """Get plain text display when rich is not available."""
        if not self.content:
            return "No file loaded"
        
        lines = []
        start_line = max(0, self.scroll_offset)
        end_line = min(len(self.content), start_line + max_lines)
        
        for i in range(start_line, end_line):
            line_content = self.content[i] if i < len(self.content) else ""
            
            if self.show_line_numbers:
                line_num = f"{i + 1:4d} │ "
                cursor_indicator = ">" if i == self.cursor_line else " "
                lines.append(f"{cursor_indicator}{line_num}{line_content}")
            else:
                cursor_indicator = ">" if i == self.cursor_line else " "
                lines.append(f"{cursor_indicator} {line_content}")
        
        return '\n'.join(lines)
    
    def _detect_language(self) -> str:
        """Detect the programming language of the current file."""
        if not self.current_file:
            return "text"
        
        extension = Path(self.current_file).suffix.lower()
        
        language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.xml': 'xml',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.md': 'markdown',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bat': 'batch',
            '.ps1': 'powershell',
            '.c': 'c',
            '.cpp': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp',
            '.java': 'java',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby',
            '.swift': 'swift',
            '.kt': 'kotlin',
            '.scala': 'scala',
            '.r': 'r',
            '.m': 'matlab',
            '.tex': 'latex',
            '.dockerfile': 'dockerfile',
        }
        
        return language_map.get(extension, 'text')
    
    def insert_text(self, text: str):
        """Insert text at the current cursor position."""
        if not self.content:
            self.content = [""]
        
        current_line = self.content[self.cursor_line]
        
        # Insert text
        new_line = (
            current_line[:self.cursor_column] + 
            text + 
            current_line[self.cursor_column:]
        )
        
        self.content[self.cursor_line] = new_line
        self.cursor_column += len(text)
        self.modified = True
    
    def delete_char(self):
        """Delete character at cursor position."""
        if not self.content:
            return
        
        current_line = self.content[self.cursor_line]
        
        if self.cursor_column > 0:
            # Delete character before cursor
            new_line = (
                current_line[:self.cursor_column - 1] + 
                current_line[self.cursor_column:]
            )
            self.content[self.cursor_line] = new_line
            self.cursor_column -= 1
            self.modified = True
        elif self.cursor_line > 0:
            # Join with previous line
            prev_line = self.content[self.cursor_line - 1]
            self.cursor_column = len(prev_line)
            self.content[self.cursor_line - 1] = prev_line + current_line
            del self.content[self.cursor_line]
            self.cursor_line -= 1
            self.modified = True
    
    def insert_newline(self):
        """Insert a new line at cursor position."""
        if not self.content:
            self.content = [""]
        
        current_line = self.content[self.cursor_line]
        
        # Split line at cursor
        line_before = current_line[:self.cursor_column]
        line_after = current_line[self.cursor_column:]
        
        self.content[self.cursor_line] = line_before
        self.content.insert(self.cursor_line + 1, line_after)
        
        self.cursor_line += 1
        self.cursor_column = 0
        self.modified = True
    
    def move_cursor(self, direction: str):
        """Move cursor in the specified direction."""
        if not self.content:
            return
        
        if direction == "up" and self.cursor_line > 0:
            self.cursor_line -= 1
            self.cursor_column = min(self.cursor_column, len(self.content[self.cursor_line]))
        
        elif direction == "down" and self.cursor_line < len(self.content) - 1:
            self.cursor_line += 1
            self.cursor_column = min(self.cursor_column, len(self.content[self.cursor_line]))
        
        elif direction == "left" and self.cursor_column > 0:
            self.cursor_column -= 1
        
        elif direction == "right":
            current_line = self.content[self.cursor_line]
            if self.cursor_column < len(current_line):
                self.cursor_column += 1
        
        elif direction == "home":
            self.cursor_column = 0
        
        elif direction == "end":
            self.cursor_column = len(self.content[self.cursor_line])
    
    def search(self, query: str):
        """Search for text in the editor."""
        self.search_query = query
        self.search_results = []
        
        if not query:
            return
        
        query_lower = query.lower()
        
        for line_idx, line in enumerate(self.content):
            line_lower = line.lower()
            start = 0
            
            while True:
                pos = line_lower.find(query_lower, start)
                if pos == -1:
                    break
                
                self.search_results.append((line_idx, pos))
                start = pos + 1
        
        self.current_search_index = 0 if self.search_results else -1
    
    def find_next(self):
        """Find next search result."""
        if not self.search_results:
            return
        
        self.current_search_index = (self.current_search_index + 1) % len(self.search_results)
        line, col = self.search_results[self.current_search_index]
        self.cursor_line = line
        self.cursor_column = col
    
    def find_previous(self):
        """Find previous search result."""
        if not self.search_results:
            return
        
        self.current_search_index = (self.current_search_index - 1) % len(self.search_results)
        line, col = self.search_results[self.current_search_index]
        self.cursor_line = line
        self.cursor_column = col
    
    def get_selected_text(self) -> str:
        """Get currently selected text (simplified implementation)."""
        # This would be more complex in a real editor with selection support
        if self.cursor_line < len(self.content):
            return self.content[self.cursor_line]
        return ""
    
    def get_line_count(self) -> int:
        """Get total number of lines."""
        return len(self.content)
    
    def get_cursor_position(self) -> Tuple[int, int]:
        """Get current cursor position (line, column)."""
        return (self.cursor_line, self.cursor_column)
    
    def goto_line(self, line_number: int):
        """Go to a specific line number."""
        line_number = max(1, min(line_number, len(self.content)))
        self.cursor_line = line_number - 1
        self.cursor_column = 0
    
    def get_file_info(self) -> Dict[str, Any]:
        """Get information about the current file."""
        return {
            'file_path': self.current_file,
            'line_count': len(self.content),
            'cursor_line': self.cursor_line + 1,
            'cursor_column': self.cursor_column + 1,
            'modified': self.modified,
            'language': self._detect_language(),
            'file_size': sum(len(line) for line in self.content),
            'search_results': len(self.search_results) if self.search_query else 0
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get editor statistics."""
        if not self.content:
            return {'lines': 0, 'characters': 0, 'words': 0}
        
        total_chars = sum(len(line) for line in self.content)
        total_words = sum(len(line.split()) for line in self.content)
        
        return {
            'lines': len(self.content),
            'characters': total_chars,
            'words': total_words,
            'language': self._detect_language(),
            'modified': self.modified
        }


def create_code_editor() -> CodeEditor:
    """
    Create and return a code editor.
    
    Returns:
        CodeEditor instance
    """
    return CodeEditor()


if __name__ == "__main__":
    # Demo mode
    editor = create_code_editor()
    
    # Load a demo file
    demo_content = '''def hello_world():
    """A simple hello world function."""
    print("Hello, World!")
    return "success"

if __name__ == "__main__":
    result = hello_world()
    print(f"Result: {result}")'''
    
    editor.content = demo_content.splitlines()
    editor.current_file = "demo.py"
    
    if RICH_AVAILABLE:
        console = Console()
        console.print(Panel(editor.get_display(), title="Code Editor Demo"))
    else:
        print("Code Editor Demo:")
        print(editor._get_plain_display())
