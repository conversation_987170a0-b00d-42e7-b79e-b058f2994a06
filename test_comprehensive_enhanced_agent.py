#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced AI Coding Agent.

Tests all the new features and improvements including:
- Modern terminal interface
- Large codebase optimization
- Superior AI capabilities
- Memory system integration
- Enhanced mode as default
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all new components can be imported."""
    print("🧪 Testing imports...")
    
    try:
        # UI components
        from ui.terminal_interface import ModernTerminalInterface
        from ui.chat_interface import ChatInterface
        from ui.file_explorer import FileExplorer
        from ui.code_editor import CodeEditor
        from ui.syntax_highlighter import SyntaxHighlighter
        print("✅ UI components imported successfully")
        
        # Optimization components
        from optimization.large_codebase_optimizer import LargeCodebaseOptimizer
        print("✅ Optimization components imported successfully")
        
        # Superior AI components
        from superior_ai.advanced_code_intelligence import AdvancedCodeIntelligence
        print("✅ Superior AI components imported successfully")
        
        # Memory tool
        from tools.memory_tool import MemoryToolSpec
        print("✅ Memory tool imported successfully")
        
        # Modern CLI
        from modern_cli import ModernEnhancedAgent
        print("✅ Modern CLI imported successfully")
        
        # Enhanced orchestrator
        from enhanced_agent_orchestrator import EnhancedAgentOrchestrator
        print("✅ Enhanced orchestrator imported successfully")
        
        return True
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False


def test_terminal_interface():
    """Test the modern terminal interface."""
    print("\n🧪 Testing modern terminal interface...")
    
    try:
        from ui.terminal_interface import ModernTerminalInterface
        
        # Create interface
        interface = ModernTerminalInterface(".")
        
        # Test basic functionality
        status = interface.get_status()
        assert status['workspace'] == str(Path(".").resolve())
        assert 'running' in status
        
        print("✅ Terminal interface created and tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Terminal interface test failed: {e}")
        return False


def test_chat_interface():
    """Test the chat interface."""
    print("\n🧪 Testing chat interface...")
    
    try:
        from ui.chat_interface import ChatInterface
        
        # Create chat interface
        chat = ChatInterface()
        
        # Test adding messages
        chat.add_user_message("Hello, AI!")
        chat.add_ai_message("Hello! How can I help you?")
        chat.add_code_message("print('Hello, World!')", "python")
        
        # Test statistics
        stats = chat.get_statistics()
        assert stats['total_messages'] >= 4  # Including welcome message
        
        print("✅ Chat interface tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Chat interface test failed: {e}")
        return False


def test_file_explorer():
    """Test the file explorer."""
    print("\n🧪 Testing file explorer...")
    
    try:
        from ui.file_explorer import FileExplorer
        
        # Create file explorer
        explorer = FileExplorer(".")
        
        # Test basic functionality
        stats = explorer.get_directory_stats()
        assert 'total_files' in stats
        assert 'total_dirs' in stats
        
        # Test file search
        results = explorer.search_files("test", max_results=5)
        assert isinstance(results, list)
        
        print("✅ File explorer tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ File explorer test failed: {e}")
        return False


def test_code_editor():
    """Test the code editor."""
    print("\n🧪 Testing code editor...")
    
    try:
        from ui.code_editor import CodeEditor
        
        # Create code editor
        editor = CodeEditor()
        
        # Test loading content
        demo_code = "def hello():\n    print('Hello, World!')\n"
        editor.content = demo_code.splitlines()
        editor.current_file = "demo.py"
        
        # Test basic operations
        info = editor.get_file_info()
        assert info['line_count'] == 2
        assert info['language'] == 'python'
        
        # Test statistics
        stats = editor.get_statistics()
        assert stats['lines'] == 2
        assert stats['words'] > 0
        
        print("✅ Code editor tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Code editor test failed: {e}")
        return False


def test_syntax_highlighter():
    """Test the syntax highlighter."""
    print("\n🧪 Testing syntax highlighter...")
    
    try:
        from ui.syntax_highlighter import SyntaxHighlighter
        
        # Create syntax highlighter
        highlighter = SyntaxHighlighter()
        
        # Test highlighting
        code = "def hello():\n    print('Hello, World!')"
        highlighted = highlighter.highlight_code(code, 'python')
        
        # Should return some form of highlighted text
        assert highlighted is not None
        
        print("✅ Syntax highlighter tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Syntax highlighter test failed: {e}")
        return False


def test_large_codebase_optimizer():
    """Test the large codebase optimizer."""
    print("\n🧪 Testing large codebase optimizer...")
    
    try:
        from optimization.large_codebase_optimizer import LargeCodebaseOptimizer
        
        # Create temporary test directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create some test files
            test_file = Path(temp_dir) / "test.py"
            test_file.write_text("def test():\n    return 'hello'\n")
            
            # Create optimizer
            optimizer = LargeCodebaseOptimizer(temp_dir)
            
            # Test scanning
            stats = optimizer.scan_codebase()
            assert 'files_indexed' in stats
            
            # Test file info
            file_info = optimizer.get_file_info(str(test_file))
            if file_info:
                assert file_info.language == 'python'
            
            print("✅ Large codebase optimizer tested successfully")
            return True
    
    except Exception as e:
        print(f"❌ Large codebase optimizer test failed: {e}")
        return False


def test_advanced_code_intelligence():
    """Test the advanced code intelligence."""
    print("\n🧪 Testing advanced code intelligence...")
    
    try:
        from superior_ai.advanced_code_intelligence import AdvancedCodeIntelligence
        
        # Create temporary test directory
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create code intelligence
            intelligence = AdvancedCodeIntelligence(temp_dir)
            
            # Test code analysis
            test_code = """
def calculate_sum(numbers):
    total = 0
    for num in numbers:
        total += num
    return total
"""
            
            analysis = intelligence.analyze_code("test.py", test_code)
            assert 'language' in analysis
            assert analysis['language'] == 'python'
            assert 'complexity' in analysis
            
            # Test code completion
            completions = intelligence.generate_code_completion(
                "test.py", test_code, 2, 10
            )
            assert isinstance(completions, list)
            
            print("✅ Advanced code intelligence tested successfully")
            return True
    
    except Exception as e:
        print(f"❌ Advanced code intelligence test failed: {e}")
        return False


def test_memory_tool():
    """Test the memory tool."""
    print("\n🧪 Testing memory tool...")
    
    try:
        from tools.memory_tool import MemoryToolSpec

        # Create memory tool
        memory = MemoryToolSpec()
        
        # Test storing and retrieving memory
        messages = list(memory.execute("Test memory content", action="store", context="test"))
        assert len(messages) > 0
        
        # Test retrieving memory
        messages = list(memory.execute("Test", action="retrieve"))
        assert len(messages) > 0
        
        print("✅ Memory tool tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Memory tool test failed: {e}")
        return False


def test_enhanced_orchestrator():
    """Test the enhanced orchestrator."""
    print("\n🧪 Testing enhanced orchestrator...")
    
    try:
        from enhanced_agent_orchestrator import EnhancedAgentOrchestrator
        
        # Create orchestrator
        orchestrator = EnhancedAgentOrchestrator()
        
        # Test basic functionality
        messages = list(orchestrator.process_request("What is Python?"))
        assert len(messages) > 0
        
        print("✅ Enhanced orchestrator tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Enhanced orchestrator test failed: {e}")
        return False


def test_modern_cli():
    """Test the modern CLI."""
    print("\n🧪 Testing modern CLI...")
    
    try:
        from modern_cli import ModernEnhancedAgent
        
        # Create agent
        agent = ModernEnhancedAgent(".")
        
        # Test initialization
        agent.initialize()
        
        # Test status
        status = agent.get_status()
        assert 'workspace' in status
        assert 'components' in status
        
        print("✅ Modern CLI tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Modern CLI test failed: {e}")
        return False


def test_tools_registry_fix():
    """Test that the tools registry fix works."""
    print("\n🧪 Testing tools registry fix...")
    
    try:
        from tools.registry import register_tool, get_tool, get_registry
        from tools.base import ToolSpec, Parameter
        
        # Test global registry functions
        registry = get_registry()
        assert registry is not None
        
        # Test that we can create and register a tool
        class TestTool(ToolSpec):
            def __init__(self):
                super().__init__(
                    name="test_tool",
                    description="A test tool",
                    parameters=[]
                )

            def execute(self, content, **kwargs):
                return []

            def is_available(self):
                return True
        
        test_tool = TestTool()
        register_tool(test_tool)
        
        # Test retrieval
        retrieved_tool = get_tool("test_tool")
        assert retrieved_tool is not None
        assert retrieved_tool.name == "test_tool"
        
        print("✅ Tools registry fix tested successfully")
        return True
    
    except Exception as e:
        print(f"❌ Tools registry test failed: {e}")
        return False


def run_comprehensive_test():
    """Run all comprehensive tests."""
    print("🚀 Starting Comprehensive Enhanced AI Agent Test Suite")
    print("=" * 70)
    
    tests = [
        test_imports,
        test_tools_registry_fix,
        test_terminal_interface,
        test_chat_interface,
        test_file_explorer,
        test_code_editor,
        test_syntax_highlighter,
        test_large_codebase_optimizer,
        test_advanced_code_intelligence,
        test_memory_tool,
        test_enhanced_orchestrator,
        test_modern_cli
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed / (passed + failed) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Enhanced AI Agent is fully functional!")
        print("🚀 Ready for production use with all superior capabilities!")
    else:
        print(f"\n⚠️ {failed} tests failed. Please review the issues above.")
    
    return failed == 0


if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
