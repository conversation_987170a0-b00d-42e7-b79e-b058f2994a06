"""
Sequential Thinking Tool for Chain of Thought Reasoning.

This tool provides advanced reasoning capabilities including Chain of Thought (CoT),
self-critique, reflection, and sequential problem-solving for complex tasks.
"""

import logging
import json
import time
from typing import Dict, List, Optional, Any, Generator, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

try:
    from tools.base import ToolSpec, Parameter
    from tools.registry import register_tool
except ImportError:
    # Fallback classes if tools not available
    class ToolSpec:
        def __init__(self, *args, **kwargs):
            self.name = kwargs.get('name', 'sequential_thinking')
            self.description = kwargs.get('description', 'Sequential thinking tool')
            self.parameters = kwargs.get('parameters', [])
            self.block_types = kwargs.get('block_types', [])

    class Parameter:
        def __init__(self, *args, **kwargs):
            pass

    def register_tool(tool):
        pass

try:
    from message import Message
except ImportError:
    class Message:
        def __init__(self, role, content, **kwargs):
            self.role = role
            self.content = content

logger = logging.getLogger(__name__)


class ThinkingType(Enum):
    """Types of thinking processes."""
    CHAIN_OF_THOUGHT = "chain_of_thought"
    SELF_CRITIQUE = "self_critique"
    REFLECTION = "reflection"
    PROBLEM_DECOMPOSITION = "problem_decomposition"
    SOLUTION_SYNTHESIS = "solution_synthesis"
    ERROR_ANALYSIS = "error_analysis"
    PLANNING = "planning"


class ThinkingStep(Enum):
    """Steps in the thinking process."""
    UNDERSTAND = "understand"
    ANALYZE = "analyze"
    HYPOTHESIZE = "hypothesize"
    EVALUATE = "evaluate"
    SYNTHESIZE = "synthesize"
    VALIDATE = "validate"
    REFLECT = "reflect"


@dataclass
class ThoughtNode:
    """Represents a single thought in the reasoning chain."""
    id: str
    content: str
    type: ThinkingType
    step: ThinkingStep
    confidence: float = 0.8
    reasoning: str = ""
    evidence: List[str] = field(default_factory=list)
    alternatives: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ThinkingSession:
    """Represents a complete thinking session."""
    id: str
    problem: str
    goal: str
    thoughts: List[ThoughtNode] = field(default_factory=list)
    current_hypothesis: Optional[str] = None
    confidence_score: float = 0.0
    iterations: int = 0
    max_iterations: int = 10
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    final_solution: Optional[str] = None
    critique_notes: List[str] = field(default_factory=list)


class SequentialThinkingTool(ToolSpec):
    """
    Advanced sequential thinking tool with CoT reasoning.
    
    Capabilities:
    - Chain of Thought reasoning
    - Self-critique and reflection
    - Problem decomposition
    - Solution synthesis
    - Error analysis and correction
    - Multi-step planning
    """
    
    def __init__(self):
        """Initialize the sequential thinking tool."""
        super().__init__(
            name="sequential_thinking",
            description="Advanced reasoning tool with Chain of Thought, self-critique, and sequential problem-solving capabilities",
            parameters=[
                Parameter(
                    name="problem",
                    type="string",
                    description="The problem or question to think through",
                    required=True
                ),
                Parameter(
                    name="thinking_type",
                    type="string",
                    description="Type of thinking process (chain_of_thought, self_critique, reflection, problem_decomposition, solution_synthesis, error_analysis, planning)",
                    required=False
                ),
                Parameter(
                    name="max_iterations",
                    type="integer",
                    description="Maximum number of thinking iterations (default: 10)",
                    required=False
                ),
                Parameter(
                    name="context",
                    type="string",
                    description="Additional context or constraints for the thinking process",
                    required=False
                ),
                Parameter(
                    name="goal",
                    type="string",
                    description="Specific goal or desired outcome",
                    required=False
                )
            ],
            block_types=["thinking", "reasoning", "cot"]
        )
        self.active_sessions: Dict[str, ThinkingSession] = {}
        self.thought_counter = 0
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute the sequential thinking process."""
        try:
            # Parse parameters
            problem = kwargs.get("problem", content)
            thinking_type = ThinkingType(kwargs.get("thinking_type", "chain_of_thought"))
            max_iterations = int(kwargs.get("max_iterations", 10))
            context = kwargs.get("context", "")
            goal = kwargs.get("goal", "Find the best solution")
            
            # Create thinking session
            session_id = f"thinking_{int(time.time())}"
            session = ThinkingSession(
                id=session_id,
                problem=problem,
                goal=goal,
                max_iterations=max_iterations
            )
            
            self.active_sessions[session_id] = session
            
            yield Message(
                role="assistant",
                content=f"🧠 **Starting Sequential Thinking Process**\n\n"
                       f"**Problem:** {problem}\n"
                       f"**Goal:** {goal}\n"
                       f"**Type:** {thinking_type.value}\n"
                       f"**Max Iterations:** {max_iterations}\n\n"
                       f"Let me think through this step by step...\n"
            )
            
            # Execute thinking process
            if thinking_type == ThinkingType.CHAIN_OF_THOUGHT:
                yield from self._chain_of_thought(session, context)
            elif thinking_type == ThinkingType.SELF_CRITIQUE:
                yield from self._self_critique(session, context)
            elif thinking_type == ThinkingType.PROBLEM_DECOMPOSITION:
                yield from self._problem_decomposition(session, context)
            elif thinking_type == ThinkingType.SOLUTION_SYNTHESIS:
                yield from self._solution_synthesis(session, context)
            elif thinking_type == ThinkingType.ERROR_ANALYSIS:
                yield from self._error_analysis(session, context)
            elif thinking_type == ThinkingType.PLANNING:
                yield from self._planning(session, context)
            else:
                yield from self._reflection(session, context)
            
            # Finalize session
            session.end_time = datetime.now()
            
            yield Message(
                role="assistant",
                content=f"\n\n✅ **Thinking Process Complete**\n\n"
                       f"**Final Solution:** {session.final_solution}\n"
                       f"**Confidence:** {session.confidence_score:.1%}\n"
                       f"**Iterations:** {session.iterations}\n"
                       f"**Duration:** {(session.end_time - session.start_time).total_seconds():.1f}s\n"
            )
            
        except Exception as e:
            logger.error(f"Error in sequential thinking: {e}")
            yield Message(
                role="assistant",
                content=f"❌ Error in thinking process: {str(e)}"
            )
    
    def _chain_of_thought(
        self,
        session: ThinkingSession,
        context: str
    ) -> Generator[Message, None, None]:
        """Execute Chain of Thought reasoning."""
        steps = [
            (ThinkingStep.UNDERSTAND, "Understanding the problem"),
            (ThinkingStep.ANALYZE, "Analyzing components and relationships"),
            (ThinkingStep.HYPOTHESIZE, "Generating potential solutions"),
            (ThinkingStep.EVALUATE, "Evaluating each solution"),
            (ThinkingStep.SYNTHESIZE, "Synthesizing the best approach"),
            (ThinkingStep.VALIDATE, "Validating the solution"),
            (ThinkingStep.REFLECT, "Reflecting on the process")
        ]
        
        for step_type, step_description in steps:
            session.iterations += 1
            
            yield Message(
                role="assistant",
                content=f"\n**Step {session.iterations}: {step_description}**\n"
            )
            
            # Generate thought for this step
            thought = self._generate_thought(session, step_type, context)
            session.thoughts.append(thought)
            
            yield Message(
                role="assistant",
                content=f"💭 {thought.content}\n"
                       f"*Confidence: {thought.confidence:.1%}*\n"
            )
            
            # Add evidence if available
            if thought.evidence:
                yield Message(
                    role="assistant",
                    content=f"📋 **Evidence:** {', '.join(thought.evidence)}\n"
                )
            
            # Add alternatives if available
            if thought.alternatives:
                yield Message(
                    role="assistant",
                    content=f"🔄 **Alternatives:** {', '.join(thought.alternatives)}\n"
                )
            
            # Update session confidence
            session.confidence_score = sum(t.confidence for t in session.thoughts) / len(session.thoughts)
            
            if session.iterations >= session.max_iterations:
                break
        
        # Generate final solution
        session.final_solution = self._synthesize_solution(session)
    
    def _self_critique(
        self,
        session: ThinkingSession,
        context: str
    ) -> Generator[Message, None, None]:
        """Execute self-critique process."""
        # First, generate initial solution
        initial_thought = self._generate_thought(
            session, 
            ThinkingStep.HYPOTHESIZE, 
            context
        )
        session.thoughts.append(initial_thought)
        
        yield Message(
            role="assistant",
            content=f"**Initial Solution:**\n💡 {initial_thought.content}\n"
        )
        
        # Iterate through critique cycles
        for iteration in range(session.max_iterations):
            session.iterations += 1
            
            # Generate critique
            critique = self._generate_critique(session, initial_thought)
            session.critique_notes.append(critique)
            
            yield Message(
                role="assistant",
                content=f"\n**Critique {iteration + 1}:**\n"
                       f"🔍 {critique}\n"
            )
            
            # Generate improved solution
            improved_thought = self._generate_improved_solution(
                session, 
                initial_thought, 
                critique
            )
            session.thoughts.append(improved_thought)
            
            yield Message(
                role="assistant",
                content=f"**Improved Solution:**\n"
                       f"✨ {improved_thought.content}\n"
                       f"*Confidence: {improved_thought.confidence:.1%}*\n"
            )
            
            # Check if improvement is significant
            if improved_thought.confidence - initial_thought.confidence < 0.05:
                yield Message(
                    role="assistant",
                    content="📈 Improvement plateau reached. Finalizing solution.\n"
                )
                break
            
            initial_thought = improved_thought
        
        session.final_solution = initial_thought.content
        session.confidence_score = initial_thought.confidence
    
    def _problem_decomposition(
        self,
        session: ThinkingSession,
        context: str
    ) -> Generator[Message, None, None]:
        """Execute problem decomposition."""
        yield Message(
            role="assistant",
            content="**Breaking down the problem into components:**\n"
        )
        
        # Identify main components
        components = self._identify_components(session.problem, context)
        
        for i, component in enumerate(components, 1):
            thought = ThoughtNode(
                id=f"component_{i}",
                content=f"Component {i}: {component}",
                type=ThinkingType.PROBLEM_DECOMPOSITION,
                step=ThinkingStep.ANALYZE,
                confidence=0.8
            )
            session.thoughts.append(thought)
            
            yield Message(
                role="assistant",
                content=f"{i}. 🧩 {component}\n"
            )
        
        # Analyze relationships
        yield Message(
            role="assistant",
            content="\n**Analyzing relationships between components:**\n"
        )
        
        relationships = self._analyze_relationships(components)
        for relationship in relationships:
            yield Message(
                role="assistant",
                content=f"🔗 {relationship}\n"
            )
        
        # Generate solution approach
        session.final_solution = self._generate_decomposed_solution(components, relationships)
        session.confidence_score = 0.85
    
    def _generate_thought(
        self,
        session: ThinkingSession,
        step: ThinkingStep,
        context: str
    ) -> ThoughtNode:
        """Generate a thought node for a specific step."""
        self.thought_counter += 1
        
        # This would typically use an LLM to generate the actual thought content
        # For now, we'll create structured placeholder content
        
        thought_content = self._create_step_content(session.problem, step, context)
        
        return ThoughtNode(
            id=f"thought_{self.thought_counter}",
            content=thought_content,
            type=ThinkingType.CHAIN_OF_THOUGHT,
            step=step,
            confidence=0.8,
            reasoning=f"Generated for step: {step.value}",
            evidence=self._gather_evidence(session.problem, step),
            alternatives=self._generate_alternatives(session.problem, step)
        )
    
    def _create_step_content(self, problem: str, step: ThinkingStep, context: str) -> str:
        """Create content for a specific thinking step."""
        step_templates = {
            ThinkingStep.UNDERSTAND: f"The problem is asking: {problem}. Key aspects to consider: {context}",
            ThinkingStep.ANALYZE: f"Breaking down the problem: {problem} involves multiple components that need careful analysis.",
            ThinkingStep.HYPOTHESIZE: f"Potential approaches to solve {problem}: 1) Direct approach, 2) Systematic approach, 3) Iterative approach",
            ThinkingStep.EVALUATE: f"Evaluating solutions for {problem}: considering feasibility, efficiency, and maintainability",
            ThinkingStep.SYNTHESIZE: f"Combining insights for {problem}: integrating the best elements from different approaches",
            ThinkingStep.VALIDATE: f"Validating the solution for {problem}: checking against requirements and constraints",
            ThinkingStep.REFLECT: f"Reflecting on the solution for {problem}: considering lessons learned and improvements"
        }
        
        return step_templates.get(step, f"Thinking about {problem} in the context of {step.value}")
    
    def _gather_evidence(self, problem: str, step: ThinkingStep) -> List[str]:
        """Gather evidence for a thinking step."""
        # Placeholder implementation - would integrate with knowledge base
        return [f"Evidence for {step.value}", f"Supporting data for {problem}"]
    
    def _generate_alternatives(self, problem: str, step: ThinkingStep) -> List[str]:
        """Generate alternative approaches for a step."""
        # Placeholder implementation
        return [f"Alternative approach 1 for {step.value}", f"Alternative approach 2 for {step.value}"]
    
    def _generate_critique(self, session: ThinkingSession, thought: ThoughtNode) -> str:
        """Generate critique for a thought."""
        # Placeholder implementation - would use LLM for actual critique
        return f"Analyzing solution: {thought.content[:50]}... Potential improvements: consider edge cases, optimize for performance, ensure maintainability."
    
    def _generate_improved_solution(
        self,
        session: ThinkingSession,
        original: ThoughtNode,
        critique: str
    ) -> ThoughtNode:
        """Generate improved solution based on critique."""
        self.thought_counter += 1
        
        return ThoughtNode(
            id=f"improved_{self.thought_counter}",
            content=f"Improved version of: {original.content} - addressing: {critique[:50]}...",
            type=ThinkingType.SELF_CRITIQUE,
            step=ThinkingStep.SYNTHESIZE,
            confidence=min(original.confidence + 0.1, 1.0),
            reasoning=f"Improved based on critique: {critique[:30]}...",
            parent_id=original.id
        )
    
    def _identify_components(self, problem: str, context: str) -> List[str]:
        """Identify main components of a problem."""
        # Placeholder implementation
        return [
            f"Core requirement from: {problem[:30]}...",
            f"Technical constraints from: {context[:30]}...",
            "Implementation considerations",
            "Testing and validation needs"
        ]
    
    def _analyze_relationships(self, components: List[str]) -> List[str]:
        """Analyze relationships between components."""
        # Placeholder implementation
        relationships = []
        for i in range(len(components) - 1):
            relationships.append(f"{components[i]} depends on {components[i+1]}")
        return relationships
    
    def _generate_decomposed_solution(
        self,
        components: List[str],
        relationships: List[str]
    ) -> str:
        """Generate solution based on decomposed components."""
        return f"Solution approach: Address {len(components)} components in sequence, considering {len(relationships)} key relationships."
    
    def _synthesize_solution(self, session: ThinkingSession) -> str:
        """Synthesize final solution from all thoughts."""
        if not session.thoughts:
            return "No solution generated"
        
        # Combine insights from all thoughts
        key_insights = [t.content for t in session.thoughts if t.confidence > 0.7]
        return f"Synthesized solution incorporating {len(key_insights)} key insights from the thinking process."

    def is_available(self) -> bool:
        """Check if the sequential thinking tool is available."""
        return True


# Register the tool
register_tool(SequentialThinkingTool())

__all__ = [
    "SequentialThinkingTool",
    "ThinkingType",
    "ThinkingStep",
    "ThoughtNode",
    "ThinkingSession",
]
