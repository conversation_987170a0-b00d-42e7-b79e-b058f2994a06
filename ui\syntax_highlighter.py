"""
Syntax Highlighter for Code Editor.

Provides syntax highlighting for multiple programming languages using rich.
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

try:
    from rich.text import Text
    from rich.syntax import Syntax
    from rich.console import Console
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


@dataclass
class HighlightRule:
    """Represents a syntax highlighting rule."""
    pattern: str
    style: str
    group: int = 0


class SyntaxHighlighter:
    """
    Advanced syntax highlighter with support for multiple languages.
    
    Features:
    - Multiple programming language support
    - Customizable color schemes
    - Keyword highlighting
    - String and comment highlighting
    - Number and operator highlighting
    - Function and class name highlighting
    """
    
    def __init__(self):
        """Initialize the syntax highlighter."""
        self.console = Console() if RICH_AVAILABLE else None
        
        # Define highlighting rules for different languages
        self.language_rules = {
            'python': self._get_python_rules(),
            'javascript': self._get_javascript_rules(),
            'typescript': self._get_typescript_rules(),
            'html': self._get_html_rules(),
            'css': self._get_css_rules(),
            'json': self._get_json_rules(),
            'yaml': self._get_yaml_rules(),
            'markdown': self._get_markdown_rules(),
            'sql': self._get_sql_rules(),
            'bash': self._get_bash_rules(),
            'c': self._get_c_rules(),
            'cpp': self._get_cpp_rules(),
            'java': self._get_java_rules(),
            'go': self._get_go_rules(),
            'rust': self._get_rust_rules(),
        }
        
        # Color scheme
        self.color_scheme = {
            'keyword': 'bold blue',
            'string': 'green',
            'comment': 'dim italic',
            'number': 'cyan',
            'operator': 'yellow',
            'function': 'bold magenta',
            'class': 'bold red',
            'variable': 'white',
            'constant': 'bold cyan',
            'type': 'bold yellow',
            'tag': 'bold blue',
            'attribute': 'magenta',
            'value': 'green',
            'error': 'bold red on white',
        }
    
    def highlight_line(self, line: str, language: str) -> Text:
        """Highlight a single line of code."""
        if not RICH_AVAILABLE:
            return line
        
        text = Text()
        
        if language not in self.language_rules:
            text.append(line)
            return text
        
        rules = self.language_rules[language]
        remaining_line = line
        position = 0
        
        while position < len(line):
            matched = False
            
            for rule in rules:
                pattern = re.compile(rule.pattern)
                match = pattern.match(remaining_line)
                
                if match:
                    matched_text = match.group(rule.group)
                    style = self.color_scheme.get(rule.style, rule.style)
                    
                    # Add any text before the match
                    if match.start() > 0:
                        text.append(remaining_line[:match.start()])
                    
                    # Add the matched text with styling
                    text.append(matched_text, style=style)
                    
                    # Update position and remaining line
                    position += match.end()
                    remaining_line = line[position:]
                    matched = True
                    break
            
            if not matched:
                # No rule matched, add the next character and continue
                text.append(line[position])
                position += 1
                remaining_line = line[position:]
        
        return text
    
    def highlight_code(self, code: str, language: str) -> Text:
        """Highlight multiple lines of code."""
        if not RICH_AVAILABLE:
            return code
        
        lines = code.split('\n')
        highlighted_text = Text()
        
        for i, line in enumerate(lines):
            if i > 0:
                highlighted_text.append('\n')
            highlighted_text.append(self.highlight_line(line, language))
        
        return highlighted_text
    
    def _get_python_rules(self) -> List[HighlightRule]:
        """Get Python syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'#.*$', 'comment'),
            
            # Strings
            HighlightRule(r'""".*?"""', 'string'),
            HighlightRule(r"'''.*?'''", 'string'),
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            HighlightRule(r'f"[^"]*"', 'string'),
            HighlightRule(r"f'[^']*'", 'string'),
            
            # Keywords
            HighlightRule(r'\b(def|class|if|elif|else|for|while|try|except|finally|with|as|import|from|return|yield|break|continue|pass|lambda|and|or|not|in|is|True|False|None)\b', 'keyword'),
            
            # Function definitions
            HighlightRule(r'\bdef\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'function', 1),
            
            # Class definitions
            HighlightRule(r'\bclass\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'class', 1),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Operators
            HighlightRule(r'[+\-*/%=<>!&|^~]', 'operator'),
        ]
    
    def _get_javascript_rules(self) -> List[HighlightRule]:
        """Get JavaScript syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'//.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            HighlightRule(r'`[^`]*`', 'string'),
            
            # Keywords
            HighlightRule(r'\b(function|var|let|const|if|else|for|while|do|switch|case|default|break|continue|return|try|catch|finally|throw|new|this|typeof|instanceof|true|false|null|undefined)\b', 'keyword'),
            
            # Function definitions
            HighlightRule(r'\bfunction\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'function', 1),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Operators
            HighlightRule(r'[+\-*/%=<>!&|^~]', 'operator'),
        ]
    
    def _get_typescript_rules(self) -> List[HighlightRule]:
        """Get TypeScript syntax highlighting rules."""
        rules = self._get_javascript_rules()
        
        # Add TypeScript-specific rules
        rules.extend([
            # Type annotations
            HighlightRule(r':\s*([a-zA-Z_][a-zA-Z0-9_]*)', 'type', 1),
            
            # Interface and type keywords
            HighlightRule(r'\b(interface|type|enum|namespace|module|declare|abstract|implements|extends)\b', 'keyword'),
        ])
        
        return rules
    
    def _get_html_rules(self) -> List[HighlightRule]:
        """Get HTML syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'<!--.*?-->', 'comment'),
            
            # Tags
            HighlightRule(r'</?([a-zA-Z][a-zA-Z0-9]*)', 'tag', 1),
            
            # Attributes
            HighlightRule(r'\s([a-zA-Z-]+)=', 'attribute', 1),
            
            # Attribute values
            HighlightRule(r'="[^"]*"', 'value'),
            HighlightRule(r"='[^']*'", 'value'),
        ]
    
    def _get_css_rules(self) -> List[HighlightRule]:
        """Get CSS syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Selectors
            HighlightRule(r'([.#]?[a-zA-Z][a-zA-Z0-9-_]*)\s*{', 'tag', 1),
            
            # Properties
            HighlightRule(r'\s*([a-zA-Z-]+)\s*:', 'attribute', 1),
            
            # Values
            HighlightRule(r':\s*([^;]+);', 'value', 1),
            
            # Numbers with units
            HighlightRule(r'\b\d+\.?\d*(px|em|rem|%|vh|vw|pt|pc|in|cm|mm)\b', 'number'),
        ]
    
    def _get_json_rules(self) -> List[HighlightRule]:
        """Get JSON syntax highlighting rules."""
        return [
            # Strings (keys and values)
            HighlightRule(r'"[^"]*"', 'string'),
            
            # Numbers
            HighlightRule(r'\b-?\d+\.?\d*([eE][+-]?\d+)?\b', 'number'),
            
            # Boolean and null
            HighlightRule(r'\b(true|false|null)\b', 'keyword'),
            
            # Operators
            HighlightRule(r'[{}[\]:,]', 'operator'),
        ]
    
    def _get_yaml_rules(self) -> List[HighlightRule]:
        """Get YAML syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'#.*$', 'comment'),
            
            # Keys
            HighlightRule(r'^(\s*)([a-zA-Z_][a-zA-Z0-9_-]*)\s*:', 'attribute', 2),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Boolean and null
            HighlightRule(r'\b(true|false|null|yes|no|on|off)\b', 'keyword'),
        ]
    
    def _get_markdown_rules(self) -> List[HighlightRule]:
        """Get Markdown syntax highlighting rules."""
        return [
            # Headers
            HighlightRule(r'^(#{1,6})\s+(.+)$', 'keyword', 1),
            
            # Bold
            HighlightRule(r'\*\*([^*]+)\*\*', 'keyword', 1),
            HighlightRule(r'__([^_]+)__', 'keyword', 1),
            
            # Italic
            HighlightRule(r'\*([^*]+)\*', 'attribute', 1),
            HighlightRule(r'_([^_]+)_', 'attribute', 1),
            
            # Code
            HighlightRule(r'`([^`]+)`', 'string', 1),
            
            # Links
            HighlightRule(r'\[([^\]]+)\]\([^)]+\)', 'function', 1),
        ]
    
    def _get_sql_rules(self) -> List[HighlightRule]:
        """Get SQL syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'--.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Keywords
            HighlightRule(r'\b(SELECT|FROM|WHERE|JOIN|INNER|LEFT|RIGHT|OUTER|ON|GROUP|BY|ORDER|HAVING|INSERT|UPDATE|DELETE|CREATE|DROP|ALTER|TABLE|INDEX|VIEW|DATABASE|SCHEMA)\b', 'keyword'),
            
            # Strings
            HighlightRule(r"'[^']*'", 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Operators
            HighlightRule(r'[=<>!]+', 'operator'),
        ]
    
    def _get_bash_rules(self) -> List[HighlightRule]:
        """Get Bash syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'#.*$', 'comment'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            
            # Variables
            HighlightRule(r'\$[a-zA-Z_][a-zA-Z0-9_]*', 'variable'),
            HighlightRule(r'\$\{[^}]+\}', 'variable'),
            
            # Keywords
            HighlightRule(r'\b(if|then|else|elif|fi|for|while|do|done|case|esac|function)\b', 'keyword'),
            
            # Commands
            HighlightRule(r'\b(echo|printf|cd|ls|pwd|mkdir|rmdir|rm|cp|mv|grep|sed|awk|sort|uniq|head|tail|cat|less|more)\b', 'function'),
        ]
    
    def _get_c_rules(self) -> List[HighlightRule]:
        """Get C syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'//.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Preprocessor directives
            HighlightRule(r'#\s*(include|define|ifdef|ifndef|endif|if|else|elif)', 'keyword'),
            
            # Keywords
            HighlightRule(r'\b(int|char|float|double|void|struct|union|enum|typedef|static|extern|const|volatile|auto|register|signed|unsigned|short|long)\b', 'type'),
            HighlightRule(r'\b(if|else|for|while|do|switch|case|default|break|continue|return|goto)\b', 'keyword'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*[fFlL]?\b', 'number'),
            
            # Function definitions
            HighlightRule(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', 'function', 1),
        ]
    
    def _get_cpp_rules(self) -> List[HighlightRule]:
        """Get C++ syntax highlighting rules."""
        rules = self._get_c_rules()
        
        # Add C++-specific rules
        rules.extend([
            # C++ keywords
            HighlightRule(r'\b(class|public|private|protected|virtual|override|namespace|using|template|typename|new|delete|this|try|catch|throw)\b', 'keyword'),
            
            # C++ types
            HighlightRule(r'\b(bool|string|vector|map|set|list|queue|stack|pair)\b', 'type'),
        ])
        
        return rules
    
    def _get_java_rules(self) -> List[HighlightRule]:
        """Get Java syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'//.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Keywords
            HighlightRule(r'\b(public|private|protected|static|final|abstract|class|interface|extends|implements|package|import)\b', 'keyword'),
            HighlightRule(r'\b(if|else|for|while|do|switch|case|default|break|continue|return|try|catch|finally|throw|throws)\b', 'keyword'),
            
            # Types
            HighlightRule(r'\b(int|long|short|byte|char|float|double|boolean|void|String|Object)\b', 'type'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*[fFdDlL]?\b', 'number'),
            
            # Class and method definitions
            HighlightRule(r'\bclass\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'class', 1),
            HighlightRule(r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', 'function', 1),
        ]
    
    def _get_go_rules(self) -> List[HighlightRule]:
        """Get Go syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'//.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Keywords
            HighlightRule(r'\b(package|import|func|var|const|type|struct|interface|map|chan|go|defer|if|else|for|range|switch|case|default|break|continue|return|fallthrough)\b', 'keyword'),
            
            # Types
            HighlightRule(r'\b(int|int8|int16|int32|int64|uint|uint8|uint16|uint32|uint64|float32|float64|complex64|complex128|bool|byte|rune|string|error)\b', 'type'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            HighlightRule(r'`[^`]*`', 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Function definitions
            HighlightRule(r'\bfunc\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'function', 1),
        ]
    
    def _get_rust_rules(self) -> List[HighlightRule]:
        """Get Rust syntax highlighting rules."""
        return [
            # Comments
            HighlightRule(r'//.*$', 'comment'),
            HighlightRule(r'/\*.*?\*/', 'comment'),
            
            # Keywords
            HighlightRule(r'\b(fn|let|mut|const|static|struct|enum|impl|trait|for|in|while|loop|if|else|match|break|continue|return|pub|mod|use|crate|super|self)\b', 'keyword'),
            
            # Types
            HighlightRule(r'\b(i8|i16|i32|i64|i128|isize|u8|u16|u32|u64|u128|usize|f32|f64|bool|char|str|String|Vec|Option|Result)\b', 'type'),
            
            # Strings
            HighlightRule(r'"[^"]*"', 'string'),
            HighlightRule(r"'[^']*'", 'string'),
            
            # Numbers
            HighlightRule(r'\b\d+\.?\d*\b', 'number'),
            
            # Function definitions
            HighlightRule(r'\bfn\s+([a-zA-Z_][a-zA-Z0-9_]*)', 'function', 1),
        ]


def create_syntax_highlighter() -> SyntaxHighlighter:
    """
    Create and return a syntax highlighter.
    
    Returns:
        SyntaxHighlighter instance
    """
    return SyntaxHighlighter()


if __name__ == "__main__":
    # Demo mode
    highlighter = create_syntax_highlighter()
    
    demo_code = '''def hello_world():
    """A simple hello world function."""
    print("Hello, World!")
    return "success"'''
    
    if RICH_AVAILABLE:
        console = Console()
        highlighted = highlighter.highlight_code(demo_code, 'python')
        console.print(highlighted)
    else:
        print("Syntax Highlighter Demo:")
        print(demo_code)
