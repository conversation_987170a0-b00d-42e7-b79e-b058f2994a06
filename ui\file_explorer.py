"""
File Explorer Component for Modern Terminal UI.

Provides an interactive file browser with tree view and file operations.
"""

import os
import stat
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
from datetime import datetime

try:
    from rich.tree import Tree
    from rich.text import Text
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class FileExplorer:
    """
    Modern file explorer with tree view and file operations.
    
    Features:
    - Tree view of project files
    - File type icons and syntax highlighting
    - File size and modification time display
    - Expandable/collapsible directories
    - File filtering and search
    - Git integration (shows git status)
    """
    
    def __init__(self, root_path: str):
        """Initialize the file explorer."""
        self.root_path = Path(root_path).resolve()
        self.expanded_dirs: Set[str] = set()
        self.selected_file: Optional[str] = None
        self.show_hidden = False
        self.file_filter = ""
        
        # File type icons
        self.file_icons = {
            '.py': '🐍',
            '.js': '📜',
            '.ts': '📘',
            '.html': '🌐',
            '.css': '🎨',
            '.json': '📋',
            '.md': '📝',
            '.txt': '📄',
            '.yml': '⚙️',
            '.yaml': '⚙️',
            '.xml': '📰',
            '.sql': '🗃️',
            '.sh': '🔧',
            '.bat': '🔧',
            '.exe': '⚙️',
            '.dll': '📚',
            '.so': '📚',
            '.zip': '📦',
            '.tar': '📦',
            '.gz': '📦',
            '.pdf': '📕',
            '.doc': '📘',
            '.docx': '📘',
            '.xls': '📊',
            '.xlsx': '📊',
            '.ppt': '📈',
            '.pptx': '📈',
            '.img': '🖼️',
            '.jpg': '🖼️',
            '.jpeg': '🖼️',
            '.png': '🖼️',
            '.gif': '🖼️',
            '.svg': '🖼️',
            '.mp3': '🎵',
            '.mp4': '🎬',
            '.avi': '🎬',
            '.mov': '🎬',
        }
        
        self.folder_icon = '📁'
        self.default_file_icon = '📄'
        
        # Expand root directory by default
        self.expanded_dirs.add(str(self.root_path))
    
    def get_tree_display(self) -> Any:
        """Get the rich tree display for the file explorer."""
        if not RICH_AVAILABLE:
            return self._get_plain_display()
        
        # Create root tree
        root_name = os.path.basename(self.root_path) or str(self.root_path)
        tree = Tree(
            f"{self.folder_icon} {root_name}",
            guide_style="blue"
        )
        
        # Build tree recursively
        self._build_tree_node(tree, self.root_path, max_depth=3)
        
        return tree
    
    def _build_tree_node(self, parent_node: Tree, path: Path, max_depth: int = 3, current_depth: int = 0):
        """Build a tree node recursively."""
        if current_depth >= max_depth:
            return
        
        try:
            # Get directory contents
            items = list(path.iterdir())
            
            # Filter hidden files if needed
            if not self.show_hidden:
                items = [item for item in items if not item.name.startswith('.')]
            
            # Apply file filter
            if self.file_filter:
                items = [item for item in items if self.file_filter.lower() in item.name.lower()]
            
            # Sort: directories first, then files
            items.sort(key=lambda x: (not x.is_dir(), x.name.lower()))
            
            for item in items:
                if item.is_dir():
                    # Directory node
                    dir_text = f"{self.folder_icon} {item.name}"
                    
                    # Add file count
                    try:
                        file_count = len([f for f in item.iterdir() if f.is_file()])
                        if file_count > 0:
                            dir_text += f" ({file_count} files)"
                    except PermissionError:
                        dir_text += " (access denied)"
                    
                    dir_node = parent_node.add(dir_text)
                    
                    # Recursively add subdirectories if expanded
                    if str(item) in self.expanded_dirs:
                        self._build_tree_node(dir_node, item, max_depth, current_depth + 1)
                
                else:
                    # File node
                    file_icon = self._get_file_icon(item)
                    file_text = f"{file_icon} {item.name}"
                    
                    # Add file size
                    try:
                        size = item.stat().st_size
                        size_str = self._format_file_size(size)
                        file_text += f" ({size_str})"
                    except (OSError, PermissionError):
                        pass
                    
                    # Highlight selected file
                    if str(item) == self.selected_file:
                        file_text = f"[bold yellow]{file_text}[/bold yellow]"
                    
                    parent_node.add(file_text)
        
        except PermissionError:
            parent_node.add("[red]Permission denied[/red]")
        except Exception as e:
            parent_node.add(f"[red]Error: {str(e)}[/red]")
    
    def _get_file_icon(self, file_path: Path) -> str:
        """Get icon for a file based on its extension."""
        extension = file_path.suffix.lower()
        return self.file_icons.get(extension, self.default_file_icon)
    
    def _format_file_size(self, size: int) -> str:
        """Format file size in human-readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f}{unit}"
            size /= 1024
        return f"{size:.1f}TB"
    
    def _get_plain_display(self) -> str:
        """Get plain text display when rich is not available."""
        lines = [f"📁 {os.path.basename(self.root_path)}"]
        
        try:
            for root, dirs, files in os.walk(self.root_path):
                level = root.replace(str(self.root_path), '').count(os.sep)
                if level > 2:  # Limit depth
                    continue
                
                indent = '  ' * level
                
                # Add directories
                for dir_name in sorted(dirs):
                    if not self.show_hidden and dir_name.startswith('.'):
                        continue
                    lines.append(f"{indent}📁 {dir_name}/")
                
                # Add files
                for file_name in sorted(files):
                    if not self.show_hidden and file_name.startswith('.'):
                        continue
                    file_path = Path(root) / file_name
                    icon = self._get_file_icon(file_path)
                    lines.append(f"{indent}{icon} {file_name}")
        
        except Exception as e:
            lines.append(f"Error: {str(e)}")
        
        return '\n'.join(lines)
    
    def expand_directory(self, dir_path: str):
        """Expand a directory in the tree view."""
        self.expanded_dirs.add(dir_path)
    
    def collapse_directory(self, dir_path: str):
        """Collapse a directory in the tree view."""
        self.expanded_dirs.discard(dir_path)
    
    def toggle_directory(self, dir_path: str):
        """Toggle directory expansion state."""
        if dir_path in self.expanded_dirs:
            self.collapse_directory(dir_path)
        else:
            self.expand_directory(dir_path)
    
    def select_file(self, file_path: str):
        """Select a file."""
        if os.path.isfile(file_path):
            self.selected_file = file_path
    
    def get_selected_file(self) -> Optional[str]:
        """Get the currently selected file."""
        return self.selected_file
    
    def set_filter(self, filter_text: str):
        """Set file filter."""
        self.file_filter = filter_text
    
    def clear_filter(self):
        """Clear file filter."""
        self.file_filter = ""
    
    def toggle_hidden_files(self):
        """Toggle showing hidden files."""
        self.show_hidden = not self.show_hidden
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get detailed information about a file."""
        try:
            path = Path(file_path)
            stat_info = path.stat()
            
            return {
                'name': path.name,
                'path': str(path),
                'size': stat_info.st_size,
                'size_formatted': self._format_file_size(stat_info.st_size),
                'modified': datetime.fromtimestamp(stat_info.st_mtime),
                'created': datetime.fromtimestamp(stat_info.st_ctime),
                'is_file': path.is_file(),
                'is_dir': path.is_dir(),
                'extension': path.suffix,
                'permissions': stat.filemode(stat_info.st_mode),
                'icon': self._get_file_icon(path) if path.is_file() else self.folder_icon
            }
        
        except Exception as e:
            return {'error': str(e)}
    
    def search_files(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for files matching the query."""
        results = []
        query_lower = query.lower()
        
        try:
            for root, dirs, files in os.walk(self.root_path):
                # Skip hidden directories if not showing hidden files
                if not self.show_hidden:
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for file_name in files:
                    if not self.show_hidden and file_name.startswith('.'):
                        continue
                    
                    if query_lower in file_name.lower():
                        file_path = Path(root) / file_name
                        results.append({
                            'name': file_name,
                            'path': str(file_path),
                            'relative_path': str(file_path.relative_to(self.root_path)),
                            'icon': self._get_file_icon(file_path)
                        })
                        
                        if len(results) >= max_results:
                            break
                
                if len(results) >= max_results:
                    break
        
        except Exception as e:
            results.append({'error': str(e)})
        
        return results
    
    def get_recent_files(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recently modified files."""
        files_with_mtime = []
        
        try:
            for root, dirs, files in os.walk(self.root_path):
                if not self.show_hidden:
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for file_name in files:
                    if not self.show_hidden and file_name.startswith('.'):
                        continue
                    
                    file_path = Path(root) / file_name
                    try:
                        mtime = file_path.stat().st_mtime
                        files_with_mtime.append((file_path, mtime))
                    except (OSError, PermissionError):
                        continue
        
        except Exception:
            pass
        
        # Sort by modification time (newest first)
        files_with_mtime.sort(key=lambda x: x[1], reverse=True)
        
        # Return top files with info
        results = []
        for file_path, mtime in files_with_mtime[:count]:
            results.append({
                'name': file_path.name,
                'path': str(file_path),
                'relative_path': str(file_path.relative_to(self.root_path)),
                'modified': datetime.fromtimestamp(mtime),
                'icon': self._get_file_icon(file_path)
            })
        
        return results
    
    def get_directory_stats(self) -> Dict[str, Any]:
        """Get statistics about the directory."""
        stats = {
            'total_files': 0,
            'total_dirs': 0,
            'total_size': 0,
            'file_types': {},
            'largest_files': []
        }
        
        files_with_size = []
        
        try:
            for root, dirs, files in os.walk(self.root_path):
                if not self.show_hidden:
                    dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                stats['total_dirs'] += len(dirs)
                
                for file_name in files:
                    if not self.show_hidden and file_name.startswith('.'):
                        continue
                    
                    file_path = Path(root) / file_name
                    try:
                        size = file_path.stat().st_size
                        stats['total_files'] += 1
                        stats['total_size'] += size
                        
                        # Track file types
                        ext = file_path.suffix.lower()
                        stats['file_types'][ext] = stats['file_types'].get(ext, 0) + 1
                        
                        # Track large files
                        files_with_size.append((str(file_path), size))
                    
                    except (OSError, PermissionError):
                        continue
        
        except Exception:
            pass
        
        # Get largest files
        files_with_size.sort(key=lambda x: x[1], reverse=True)
        stats['largest_files'] = [
            {
                'path': path,
                'size': size,
                'size_formatted': self._format_file_size(size)
            }
            for path, size in files_with_size[:10]
        ]
        
        stats['total_size_formatted'] = self._format_file_size(stats['total_size'])
        
        return stats


def create_file_explorer(root_path: str) -> FileExplorer:
    """
    Create and return a file explorer.
    
    Args:
        root_path: Root directory path
    
    Returns:
        FileExplorer instance
    """
    return FileExplorer(root_path)


if __name__ == "__main__":
    # Demo mode
    explorer = create_file_explorer(".")
    
    if RICH_AVAILABLE:
        console = Console()
        console.print(Panel(explorer.get_tree_display(), title="File Explorer Demo"))
    else:
        print("File Explorer Demo:")
        print(explorer._get_plain_display())
