"""
Chat Interface Component for Modern Terminal UI.

Provides real-time chat interface with syntax highlighting and message formatting.
"""

import re
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import dataclass

try:
    from rich.console import Console
    from rich.text import Text
    from rich.panel import Panel
    from rich.markdown import Markdown
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.columns import Columns
    from rich.align import Align
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


@dataclass
class ChatMessage:
    """Represents a chat message."""
    role: str  # user, assistant, system
    content: str
    timestamp: datetime
    message_type: str = "text"  # text, code, error, success
    metadata: Dict[str, Any] = None


class ChatInterface:
    """
    Modern chat interface with syntax highlighting and rich formatting.
    
    Features:
    - Real-time message display
    - Syntax highlighting for code blocks
    - Message type formatting (user, AI, system)
    - Scrollable message history
    - Rich text rendering
    """
    
    def __init__(self, max_messages: int = 100):
        """Initialize the chat interface."""
        self.messages: List[ChatMessage] = []
        self.max_messages = max_messages
        self.console = Console() if RICH_AVAILABLE else None
        
        # Add welcome message
        self.add_system_message(
            "🚀 Welcome to Enhanced AI Coding Agent!\n"
            "I'm here to help you with coding, analysis, and development tasks.\n"
            "Features: Chain of Thought reasoning, Advanced research, Multi-file coordination"
        )
    
    def add_user_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a user message to the chat."""
        message = ChatMessage(
            role="user",
            content=content,
            timestamp=datetime.now(),
            message_type="text",
            metadata=metadata or {}
        )
        self._add_message(message)
    
    def add_ai_message(self, content: str, message_type: str = "text", metadata: Optional[Dict[str, Any]] = None):
        """Add an AI assistant message to the chat."""
        message = ChatMessage(
            role="assistant",
            content=content,
            timestamp=datetime.now(),
            message_type=message_type,
            metadata=metadata or {}
        )
        self._add_message(message)
    
    def add_system_message(self, content: str, metadata: Optional[Dict[str, Any]] = None):
        """Add a system message to the chat."""
        message = ChatMessage(
            role="system",
            content=content,
            timestamp=datetime.now(),
            message_type="text",
            metadata=metadata or {}
        )
        self._add_message(message)
    
    def add_code_message(self, code: str, language: str = "python", role: str = "assistant"):
        """Add a code message with syntax highlighting."""
        message = ChatMessage(
            role=role,
            content=code,
            timestamp=datetime.now(),
            message_type="code",
            metadata={"language": language}
        )
        self._add_message(message)
    
    def add_error_message(self, error: str, role: str = "system"):
        """Add an error message."""
        message = ChatMessage(
            role=role,
            content=error,
            timestamp=datetime.now(),
            message_type="error"
        )
        self._add_message(message)
    
    def add_success_message(self, success: str, role: str = "system"):
        """Add a success message."""
        message = ChatMessage(
            role=role,
            content=success,
            timestamp=datetime.now(),
            message_type="success"
        )
        self._add_message(message)
    
    def _add_message(self, message: ChatMessage):
        """Add a message and maintain max_messages limit."""
        self.messages.append(message)
        
        # Keep only the last max_messages
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def get_display(self) -> Any:
        """Get the rich display for the chat interface."""
        if not RICH_AVAILABLE:
            return self._get_plain_display()
        
        if not self.messages:
            return Text("No messages yet. Start a conversation!", style="dim italic")
        
        # Create message display
        message_texts = []
        
        for message in self.messages[-20:]:  # Show last 20 messages
            formatted_message = self._format_message(message)
            message_texts.append(formatted_message)
        
        # Join messages with spacing
        display_text = Text()
        for i, msg_text in enumerate(message_texts):
            if i > 0:
                display_text.append("\n")
            display_text.append(msg_text)
        
        return display_text
    
    def _format_message(self, message: ChatMessage) -> Text:
        """Format a single message for display."""
        if not RICH_AVAILABLE:
            return f"[{message.timestamp.strftime('%H:%M')}] {message.role}: {message.content}"
        
        # Create timestamp
        timestamp = message.timestamp.strftime("%H:%M")
        
        # Create role prefix with styling
        role_styles = {
            "user": "bold blue",
            "assistant": "bold green", 
            "system": "bold yellow"
        }
        
        role_icons = {
            "user": "💬",
            "assistant": "🤖",
            "system": "ℹ️"
        }
        
        role_style = role_styles.get(message.role, "white")
        role_icon = role_icons.get(message.role, "•")
        
        # Create message text
        text = Text()
        
        # Add timestamp
        text.append(f"[{timestamp}] ", style="dim")
        
        # Add role with icon
        text.append(f"{role_icon} {message.role.title()}: ", style=role_style)
        
        # Add content based on message type
        if message.message_type == "code":
            # Handle code blocks
            language = message.metadata.get("language", "text") if message.metadata else "text"
            try:
                if RICH_AVAILABLE:
                    syntax = Syntax(message.content, language, theme="monokai", line_numbers=True)
                    # For now, just add the code as text since we can't embed Syntax in Text
                    text.append(f"\n```{language}\n{message.content}\n```", style="cyan")
                else:
                    text.append(f"\n{message.content}", style="cyan")
            except:
                text.append(f"\n{message.content}", style="cyan")
        
        elif message.message_type == "error":
            text.append(message.content, style="bold red")
        
        elif message.message_type == "success":
            text.append(message.content, style="bold green")
        
        else:
            # Regular text - check for markdown-like formatting
            formatted_content = self._format_text_content(message.content)
            text.append(formatted_content)
        
        return text
    
    def _format_text_content(self, content: str) -> Text:
        """Format text content with basic markdown-like styling."""
        if not RICH_AVAILABLE:
            return content
        
        text = Text()
        
        # Split content into lines for processing
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if i > 0:
                text.append('\n')
            
            # Check for special formatting
            if line.startswith('**') and line.endswith('**') and len(line) > 4:
                # Bold text
                text.append(line[2:-2], style="bold")
            elif line.startswith('*') and line.endswith('*') and len(line) > 2:
                # Italic text
                text.append(line[1:-1], style="italic")
            elif line.startswith('`') and line.endswith('`') and len(line) > 2:
                # Inline code
                text.append(line[1:-1], style="cyan")
            elif line.startswith('# '):
                # Header
                text.append(line[2:], style="bold blue")
            elif line.startswith('## '):
                # Subheader
                text.append(line[3:], style="bold cyan")
            elif line.startswith('- ') or line.startswith('• '):
                # List item
                text.append(line, style="white")
            elif line.startswith('✅') or line.startswith('❌') or line.startswith('⚠️'):
                # Status indicators
                text.append(line, style="white")
            else:
                # Regular text
                text.append(line, style="white")
        
        return text
    
    def _get_plain_display(self) -> str:
        """Get plain text display when rich is not available."""
        if not self.messages:
            return "No messages yet. Start a conversation!"
        
        display_lines = []
        for message in self.messages[-10:]:  # Show last 10 messages
            timestamp = message.timestamp.strftime("%H:%M")
            role_icon = {"user": "You", "assistant": "AI", "system": "System"}.get(message.role, message.role)
            display_lines.append(f"[{timestamp}] {role_icon}: {message.content}")
        
        return "\n".join(display_lines)
    
    def clear_messages(self):
        """Clear all messages."""
        self.messages.clear()
        self.add_system_message("Chat cleared.")
    
    def get_message_count(self) -> int:
        """Get the total number of messages."""
        return len(self.messages)
    
    def get_recent_messages(self, count: int = 5) -> List[ChatMessage]:
        """Get the most recent messages."""
        return self.messages[-count:] if self.messages else []
    
    def search_messages(self, query: str) -> List[ChatMessage]:
        """Search messages by content."""
        query_lower = query.lower()
        return [
            msg for msg in self.messages
            if query_lower in msg.content.lower()
        ]
    
    def export_chat_history(self) -> str:
        """Export chat history as formatted text."""
        if not self.messages:
            return "No chat history to export."
        
        lines = ["# Chat History Export", f"Generated: {datetime.now().isoformat()}", ""]
        
        for message in self.messages:
            timestamp = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            lines.append(f"## [{timestamp}] {message.role.title()}")
            lines.append("")
            lines.append(message.content)
            lines.append("")
        
        return "\n".join(lines)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get chat statistics."""
        if not self.messages:
            return {"total_messages": 0}
        
        role_counts = {}
        type_counts = {}
        
        for message in self.messages:
            role_counts[message.role] = role_counts.get(message.role, 0) + 1
            type_counts[message.message_type] = type_counts.get(message.message_type, 0) + 1
        
        return {
            "total_messages": len(self.messages),
            "role_distribution": role_counts,
            "type_distribution": type_counts,
            "first_message": self.messages[0].timestamp.isoformat(),
            "last_message": self.messages[-1].timestamp.isoformat()
        }


def create_chat_interface(max_messages: int = 100) -> ChatInterface:
    """
    Create and return a chat interface.
    
    Args:
        max_messages: Maximum number of messages to keep in memory
    
    Returns:
        ChatInterface instance
    """
    return ChatInterface(max_messages)


if __name__ == "__main__":
    # Demo mode
    chat = create_chat_interface()
    
    # Add some demo messages
    chat.add_user_message("Hello! Can you help me with Python?")
    chat.add_ai_message("Of course! I'd be happy to help you with Python. What specific topic would you like to explore?")
    chat.add_code_message("print('Hello, World!')", "python")
    chat.add_success_message("Code executed successfully!")
    
    # Display
    if RICH_AVAILABLE:
        console = Console()
        console.print(Panel(chat.get_display(), title="Chat Demo"))
    else:
        print("Chat Demo:")
        print(chat._get_plain_display())
