"""
Advanced Codebase Analyzer for Enhanced Code Understanding.

This tool provides comprehensive codebase analysis capabilities including:
- Advanced code analysis and indexing
- Multi-file coordination and relationship mapping
- Project-level reasoning and understanding
- Intelligent context retrieval
- Code pattern recognition and architectural analysis
"""

import os
import ast
import json
import logging
import hashlib
from typing import Dict, Any, List, Optional, Set, Tuple, Generator
from dataclasses import dataclass, asdict
from pathlib import Path
from collections import defaultdict
import re

from tools.base import ToolSpec, Parameter
from message import Message

logger = logging.getLogger(__name__)


@dataclass
class CodeEntity:
    """Represents a code entity (class, function, variable, etc.)."""
    name: str
    entity_type: str  # 'class', 'function', 'variable', 'import', 'constant'
    file_path: str
    line_number: int
    end_line: Optional[int] = None
    signature: Optional[str] = None
    docstring: Optional[str] = None
    dependencies: List[str] = None
    complexity_score: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class FileAnalysis:
    """Represents analysis results for a single file."""
    file_path: str
    language: str
    entities: List[CodeEntity]
    imports: List[str]
    exports: List[str]
    complexity_metrics: Dict[str, float]
    dependencies: List[str]
    size_metrics: Dict[str, int]
    last_modified: str
    analysis_timestamp: str


@dataclass
class ProjectAnalysis:
    """Represents comprehensive project analysis."""
    project_root: str
    files: Dict[str, FileAnalysis]
    dependency_graph: Dict[str, List[str]]
    architecture_patterns: List[str]
    technology_stack: List[str]
    complexity_summary: Dict[str, float]
    hotspots: List[str]  # Files that need attention
    recommendations: List[str]


class AdvancedCodebaseAnalyzer(ToolSpec):
    """
    Advanced codebase analyzer with intelligent code understanding capabilities.
    
    This tool provides:
    - Deep code analysis and indexing
    - Multi-file relationship mapping
    - Project-level architectural understanding
    - Intelligent context retrieval
    - Code quality and complexity analysis
    - Refactoring suggestions
    """
    
    def __init__(self):
        parameters = [
            Parameter(
                name="analysis_type",
                type="string",
                description="Type of analysis to perform",
                required=False,
                default="comprehensive",
                enum=["comprehensive", "quick", "focused", "dependency", "complexity", "architecture"]
            ),
            Parameter(
                name="target_path",
                type="string",
                description="Path to analyze (file or directory)",
                required=False,
                default="."
            ),
            Parameter(
                name="include_patterns",
                type="string",
                description="File patterns to include (comma-separated)",
                required=False,
                default="*.py,*.js,*.ts,*.java,*.cpp,*.h,*.cs,*.go,*.rs"
            ),
            Parameter(
                name="exclude_patterns",
                type="string",
                description="File patterns to exclude (comma-separated)",
                required=False,
                default="node_modules,__pycache__,.git,build,dist,target"
            ),
            Parameter(
                name="max_depth",
                type="integer",
                description="Maximum directory depth to analyze",
                required=False,
                default=10
            ),
            Parameter(
                name="focus_entity",
                type="string",
                description="Specific entity to focus analysis on (class, function, etc.)",
                required=False
            ),
            Parameter(
                name="include_metrics",
                type="boolean",
                description="Whether to include detailed complexity metrics",
                required=False,
                default=True
            ),
            Parameter(
                name="generate_recommendations",
                type="boolean",
                description="Whether to generate improvement recommendations",
                required=False,
                default=True
            )
        ]
        
        super().__init__(
            name="advanced_codebase_analyzer",
            description="Advanced codebase analyzer with intelligent code understanding, multi-file coordination, and project-level reasoning capabilities",
            parameters=parameters,
            block_types=["codebase_analyzer", "code_analysis", "project_analysis"]
        )
        
        # Language-specific analyzers
        self.language_analyzers = {
            ".py": self._analyze_python_file,
            ".js": self._analyze_javascript_file,
            ".ts": self._analyze_typescript_file,
            ".java": self._analyze_java_file,
            ".cpp": self._analyze_cpp_file,
            ".h": self._analyze_cpp_file,
            ".cs": self._analyze_csharp_file,
            ".go": self._analyze_go_file,
            ".rs": self._analyze_rust_file
        }
        
        # Architecture patterns to detect
        self.architecture_patterns = {
            "mvc": ["model", "view", "controller"],
            "mvp": ["model", "view", "presenter"],
            "mvvm": ["model", "view", "viewmodel"],
            "microservices": ["service", "api", "gateway"],
            "layered": ["controller", "service", "repository", "dao"],
            "hexagonal": ["port", "adapter", "domain"],
            "clean": ["entity", "usecase", "interface", "framework"]
        }
    
    def is_available(self) -> bool:
        """Check if the tool is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute the codebase analysis."""
        try:
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            
            # Determine target path
            target_path = params.get("target_path", ".")
            if not os.path.exists(target_path):
                yield self.create_response(f"❌ Path not found: {target_path}")
                return
            
            yield self.create_response(f"🔍 Starting {params['analysis_type']} analysis of: {target_path}")
            
            # Perform analysis based on type
            if params["analysis_type"] == "comprehensive":
                yield from self._comprehensive_analysis(target_path, params)
            elif params["analysis_type"] == "quick":
                yield from self._quick_analysis(target_path, params)
            elif params["analysis_type"] == "focused":
                yield from self._focused_analysis(target_path, params)
            elif params["analysis_type"] == "dependency":
                yield from self._dependency_analysis(target_path, params)
            elif params["analysis_type"] == "complexity":
                yield from self._complexity_analysis(target_path, params)
            elif params["analysis_type"] == "architecture":
                yield from self._architecture_analysis(target_path, params)
            
        except Exception as e:
            logger.error(f"Error in codebase analysis: {e}")
            yield self.create_response(f"Error in codebase analysis: {str(e)}")
    
    def _comprehensive_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform comprehensive codebase analysis."""
        # Discover files
        files = self._discover_files(target_path, params)
        yield self.create_response(f"📁 Found {len(files)} files to analyze")
        
        # Analyze each file
        file_analyses = {}
        for i, file_path in enumerate(files):
            if i % 10 == 0:  # Progress update every 10 files
                yield self.create_response(f"📊 Analyzing file {i+1}/{len(files)}: {os.path.basename(file_path)}")
            
            analysis = self._analyze_file(file_path)
            if analysis:
                file_analyses[file_path] = analysis
        
        # Build project analysis
        project_analysis = self._build_project_analysis(target_path, file_analyses)
        
        # Generate comprehensive report
        report = self._generate_comprehensive_report(project_analysis)
        yield self.create_response(report)
        
        # Generate recommendations if requested
        if params["generate_recommendations"]:
            recommendations = self._generate_recommendations(project_analysis)
            yield self.create_response(f"## 💡 Recommendations\n\n{recommendations}")
    
    def _quick_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform quick analysis focusing on high-level metrics."""
        files = self._discover_files(target_path, params)
        
        # Quick metrics
        total_files = len(files)
        total_lines = 0
        language_distribution = defaultdict(int)
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = len(f.readlines())
                    total_lines += lines
                
                ext = Path(file_path).suffix.lower()
                language_distribution[ext] += 1
            except Exception:
                continue
        
        # Generate quick report
        report_parts = [
            "## 📊 Quick Analysis Results",
            f"**Total Files:** {total_files}",
            f"**Total Lines of Code:** {total_lines:,}",
            "",
            "### Language Distribution"
        ]
        
        for ext, count in sorted(language_distribution.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_files) * 100
            report_parts.append(f"- {ext or 'no extension'}: {count} files ({percentage:.1f}%)")
        
        yield self.create_response("\n".join(report_parts))
    
    def _focused_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform focused analysis on specific entity."""
        focus_entity = params.get("focus_entity")
        if not focus_entity:
            yield self.create_response("❌ focus_entity parameter required for focused analysis")
            return
        
        files = self._discover_files(target_path, params)
        matches = []
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis:
                for entity in analysis.entities:
                    if focus_entity.lower() in entity.name.lower():
                        matches.append((file_path, entity))
        
        if not matches:
            yield self.create_response(f"❌ No entities found matching: {focus_entity}")
            return
        
        # Generate focused report
        report_parts = [f"## 🎯 Focused Analysis: {focus_entity}", f"Found {len(matches)} matches:\n"]
        
        for file_path, entity in matches:
            report_parts.append(f"### {entity.name} ({entity.entity_type})")
            report_parts.append(f"**File:** {file_path}")
            report_parts.append(f"**Line:** {entity.line_number}")
            if entity.signature:
                report_parts.append(f"**Signature:** `{entity.signature}`")
            if entity.docstring:
                report_parts.append(f"**Documentation:** {entity.docstring[:200]}...")
            report_parts.append("")
        
        yield self.create_response("\n".join(report_parts))
    
    def _dependency_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform dependency analysis."""
        files = self._discover_files(target_path, params)
        dependency_graph = defaultdict(set)
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis:
                for dep in analysis.dependencies:
                    dependency_graph[file_path].add(dep)
        
        # Find circular dependencies
        circular_deps = self._find_circular_dependencies(dependency_graph)
        
        # Generate dependency report
        report_parts = [
            "## 🔗 Dependency Analysis",
            f"**Total Files:** {len(files)}",
            f"**Files with Dependencies:** {len(dependency_graph)}",
            f"**Circular Dependencies Found:** {len(circular_deps)}",
            ""
        ]
        
        if circular_deps:
            report_parts.append("### ⚠️ Circular Dependencies")
            for cycle in circular_deps[:5]:  # Show first 5 cycles
                report_parts.append(f"- {' → '.join(cycle)}")
            report_parts.append("")
        
        # Top dependencies
        all_deps = defaultdict(int)
        for deps in dependency_graph.values():
            for dep in deps:
                all_deps[dep] += 1
        
        if all_deps:
            report_parts.append("### 📊 Most Common Dependencies")
            for dep, count in sorted(all_deps.items(), key=lambda x: x[1], reverse=True)[:10]:
                report_parts.append(f"- {dep}: {count} files")
        
        yield self.create_response("\n".join(report_parts))
    
    def _complexity_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform complexity analysis."""
        files = self._discover_files(target_path, params)
        complexity_data = []
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis and analysis.complexity_metrics:
                complexity_data.append((file_path, analysis.complexity_metrics))
        
        if not complexity_data:
            yield self.create_response("❌ No complexity data available")
            return
        
        # Calculate overall metrics
        total_complexity = sum(metrics.get("cyclomatic", 0) for _, metrics in complexity_data)
        avg_complexity = total_complexity / len(complexity_data) if complexity_data else 0
        
        # Find high complexity files
        high_complexity = [(path, metrics) for path, metrics in complexity_data 
                          if metrics.get("cyclomatic", 0) > avg_complexity * 1.5]
        
        # Generate complexity report
        report_parts = [
            "## 📈 Complexity Analysis",
            f"**Files Analyzed:** {len(complexity_data)}",
            f"**Average Cyclomatic Complexity:** {avg_complexity:.2f}",
            f"**High Complexity Files:** {len(high_complexity)}",
            ""
        ]
        
        if high_complexity:
            report_parts.append("### ⚠️ High Complexity Files")
            for path, metrics in sorted(high_complexity, key=lambda x: x[1].get("cyclomatic", 0), reverse=True)[:10]:
                complexity = metrics.get("cyclomatic", 0)
                report_parts.append(f"- {os.path.basename(path)}: {complexity:.1f}")
        
        yield self.create_response("\n".join(report_parts))
    
    def _architecture_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform architectural pattern analysis."""
        files = self._discover_files(target_path, params)
        
        # Analyze directory structure
        directories = set()
        for file_path in files:
            directories.add(os.path.dirname(file_path))
        
        # Detect architecture patterns
        detected_patterns = []
        for pattern_name, keywords in self.architecture_patterns.items():
            score = 0
            for directory in directories:
                dir_name = os.path.basename(directory).lower()
                for keyword in keywords:
                    if keyword in dir_name:
                        score += 1
            
            if score >= len(keywords) * 0.5:  # At least 50% of keywords found
                detected_patterns.append((pattern_name, score))
        
        # Generate architecture report
        report_parts = [
            "## 🏗️ Architecture Analysis",
            f"**Directories Analyzed:** {len(directories)}",
            f"**Patterns Detected:** {len(detected_patterns)}",
            ""
        ]
        
        if detected_patterns:
            report_parts.append("### 🎯 Detected Patterns")
            for pattern, score in sorted(detected_patterns, key=lambda x: x[1], reverse=True):
                confidence = min(score / len(self.architecture_patterns[pattern]), 1.0) * 100
                report_parts.append(f"- {pattern.upper()}: {confidence:.0f}% confidence")
        else:
            report_parts.append("### 🤔 No Clear Architectural Patterns Detected")
            report_parts.append("Consider organizing code into clearer architectural patterns.")
        
        yield self.create_response("\n".join(report_parts))
    
    def _discover_files(self, target_path: str, params: Dict[str, Any]) -> List[str]:
        """Discover files to analyze based on patterns."""
        include_patterns = params.get("include_patterns", "").split(",")
        exclude_patterns = params.get("exclude_patterns", "").split(",")
        max_depth = params.get("max_depth", 10)
        
        files = []
        
        if os.path.isfile(target_path):
            return [target_path]
        
        for root, dirs, filenames in os.walk(target_path):
            # Check depth
            depth = root[len(target_path):].count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't recurse deeper
                continue
            
            # Filter directories
            dirs[:] = [d for d in dirs if not any(pattern.strip() in d for pattern in exclude_patterns if pattern.strip())]
            
            for filename in filenames:
                file_path = os.path.join(root, filename)
                
                # Check include patterns
                if include_patterns and include_patterns != ['']:
                    if not any(self._matches_pattern(filename, pattern.strip()) for pattern in include_patterns):
                        continue
                
                # Check exclude patterns
                if any(pattern.strip() in file_path for pattern in exclude_patterns if pattern.strip()):
                    continue
                
                files.append(file_path)
        
        return files
    
    def _matches_pattern(self, filename: str, pattern: str) -> bool:
        """Check if filename matches pattern (supports wildcards)."""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def _analyze_file(self, file_path: str) -> Optional[FileAnalysis]:
        """Analyze a single file."""
        try:
            ext = Path(file_path).suffix.lower()
            if ext in self.language_analyzers:
                return self.language_analyzers[ext](file_path)
            else:
                return self._analyze_generic_file(file_path)
        except Exception as e:
            logger.warning(f"Failed to analyze {file_path}: {e}")
            return None
    
    def _analyze_python_file(self, file_path: str) -> FileAnalysis:
        """Analyze Python file."""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        entities = []
        imports = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        entity_type="function",
                        file_path=file_path,
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        signature=f"def {node.name}({', '.join(arg.arg for arg in node.args.args)})",
                        docstring=ast.get_docstring(node),
                        complexity_score=self._calculate_complexity(node)
                    ))
                
                elif isinstance(node, ast.ClassDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        entity_type="class",
                        file_path=file_path,
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        signature=f"class {node.name}",
                        docstring=ast.get_docstring(node)
                    ))
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        module = node.module or ""
                        imports.append(module)
        
        except SyntaxError:
            pass  # Skip files with syntax errors
        
        # Calculate metrics
        lines = content.split('\n')
        complexity_metrics = {
            "cyclomatic": sum(entity.complexity_score for entity in entities if entity.entity_type == "function"),
            "lines_of_code": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "comment_ratio": len([line for line in lines if line.strip().startswith('#')]) / len(lines) if lines else 0
        }
        
        size_metrics = {
            "total_lines": len(lines),
            "code_lines": complexity_metrics["lines_of_code"],
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "blank_lines": len([line for line in lines if not line.strip()])
        }
        
        return FileAnalysis(
            file_path=file_path,
            language="python",
            entities=entities,
            imports=imports,
            exports=[],  # Python doesn't have explicit exports
            complexity_metrics=complexity_metrics,
            dependencies=imports,
            size_metrics=size_metrics,
            last_modified=str(os.path.getmtime(file_path)),
            analysis_timestamp=str(hash(content))
        )
    
    def _analyze_javascript_file(self, file_path: str) -> FileAnalysis:
        """Analyze JavaScript file (simplified)."""
        return self._analyze_generic_file(file_path, "javascript")
    
    def _analyze_typescript_file(self, file_path: str) -> FileAnalysis:
        """Analyze TypeScript file (simplified)."""
        return self._analyze_generic_file(file_path, "typescript")
    
    def _analyze_java_file(self, file_path: str) -> FileAnalysis:
        """Analyze Java file (simplified)."""
        return self._analyze_generic_file(file_path, "java")
    
    def _analyze_cpp_file(self, file_path: str) -> FileAnalysis:
        """Analyze C++ file (simplified)."""
        return self._analyze_generic_file(file_path, "cpp")
    
    def _analyze_csharp_file(self, file_path: str) -> FileAnalysis:
        """Analyze C# file (simplified)."""
        return self._analyze_generic_file(file_path, "csharp")
    
    def _analyze_go_file(self, file_path: str) -> FileAnalysis:
        """Analyze Go file (simplified)."""
        return self._analyze_generic_file(file_path, "go")
    
    def _analyze_rust_file(self, file_path: str) -> FileAnalysis:
        """Analyze Rust file (simplified)."""
        return self._analyze_generic_file(file_path, "rust")
    
    def _analyze_generic_file(self, file_path: str, language: str = "unknown") -> FileAnalysis:
        """Generic file analysis for unsupported languages."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Basic metrics
            size_metrics = {
                "total_lines": len(lines),
                "code_lines": len([line for line in lines if line.strip()]),
                "blank_lines": len([line for line in lines if not line.strip()])
            }
            
            complexity_metrics = {
                "lines_of_code": size_metrics["code_lines"],
                "cyclomatic": 1  # Default complexity
            }
            
            return FileAnalysis(
                file_path=file_path,
                language=language,
                entities=[],
                imports=[],
                exports=[],
                complexity_metrics=complexity_metrics,
                dependencies=[],
                size_metrics=size_metrics,
                last_modified=str(os.path.getmtime(file_path)),
                analysis_timestamp=str(hash(content))
            )
        
        except Exception:
            return None
    
    def _calculate_complexity(self, node: ast.AST) -> float:
        """Calculate cyclomatic complexity for an AST node."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _build_project_analysis(self, project_root: str, file_analyses: Dict[str, FileAnalysis]) -> ProjectAnalysis:
        """Build comprehensive project analysis from file analyses."""
        # Build dependency graph
        dependency_graph = {}
        for file_path, analysis in file_analyses.items():
            dependency_graph[file_path] = analysis.dependencies
        
        # Detect technology stack
        technology_stack = set()
        for analysis in file_analyses.values():
            technology_stack.add(analysis.language)
            technology_stack.update(analysis.imports[:5])  # Top 5 imports
        
        # Calculate complexity summary
        total_complexity = sum(analysis.complexity_metrics.get("cyclomatic", 0) for analysis in file_analyses.values())
        total_files = len(file_analyses)
        
        complexity_summary = {
            "total_complexity": total_complexity,
            "average_complexity": total_complexity / total_files if total_files else 0,
            "total_files": total_files,
            "total_lines": sum(analysis.size_metrics.get("total_lines", 0) for analysis in file_analyses.values())
        }
        
        # Identify hotspots (high complexity files)
        avg_complexity = complexity_summary["average_complexity"]
        hotspots = [
            file_path for file_path, analysis in file_analyses.items()
            if analysis.complexity_metrics.get("cyclomatic", 0) > avg_complexity * 2
        ]
        
        return ProjectAnalysis(
            project_root=project_root,
            files=file_analyses,
            dependency_graph=dependency_graph,
            architecture_patterns=[],  # Will be filled by architecture analysis
            technology_stack=list(technology_stack),
            complexity_summary=complexity_summary,
            hotspots=hotspots,
            recommendations=[]
        )
    
    def _find_circular_dependencies(self, dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Find circular dependencies in the dependency graph."""
        # Simplified cycle detection
        cycles = []
        visited = set()
        
        def dfs(node, path):
            if node in path:
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            
            for neighbor in dependency_graph.get(node, []):
                if neighbor in dependency_graph:  # Only follow internal dependencies
                    dfs(neighbor, path.copy())
        
        for node in dependency_graph:
            if node not in visited:
                dfs(node, [])
        
        return cycles[:10]  # Return first 10 cycles
    
    def _generate_comprehensive_report(self, project_analysis: ProjectAnalysis) -> str:
        """Generate comprehensive analysis report."""
        report_parts = [
            "## 📊 Comprehensive Codebase Analysis",
            f"**Project Root:** {project_analysis.project_root}",
            f"**Files Analyzed:** {project_analysis.complexity_summary['total_files']}",
            f"**Total Lines of Code:** {project_analysis.complexity_summary['total_lines']:,}",
            f"**Average Complexity:** {project_analysis.complexity_summary['average_complexity']:.2f}",
            "",
            "### 🛠️ Technology Stack"
        ]
        
        for tech in sorted(project_analysis.technology_stack)[:10]:
            if tech and tech != "unknown":
                report_parts.append(f"- {tech}")
        
        if project_analysis.hotspots:
            report_parts.append("\n### ⚠️ Complexity Hotspots")
            for hotspot in project_analysis.hotspots[:5]:
                report_parts.append(f"- {os.path.basename(hotspot)}")
        
        return "\n".join(report_parts)
    
    def _generate_recommendations(self, project_analysis: ProjectAnalysis) -> str:
        """Generate improvement recommendations."""
        recommendations = []
        
        # Complexity recommendations
        if project_analysis.complexity_summary["average_complexity"] > 10:
            recommendations.append("Consider refactoring high-complexity functions to improve maintainability")
        
        # Hotspot recommendations
        if len(project_analysis.hotspots) > project_analysis.complexity_summary["total_files"] * 0.1:
            recommendations.append("Multiple complexity hotspots detected - prioritize refactoring efforts")
        
        # Architecture recommendations
        if not project_analysis.architecture_patterns:
            recommendations.append("Consider adopting a clear architectural pattern for better code organization")
        
        # Default recommendations
        if not recommendations:
            recommendations = [
                "Codebase appears well-structured",
                "Consider adding more comprehensive documentation",
                "Regular code reviews can help maintain quality"
            ]
        
        return "\n".join(f"- {rec}" for rec in recommendations)


# Register the tool
def register_tool():
    """Register the advanced codebase analyzer."""
    from tools.registry import get_registry
    registry = get_registry()
    registry.register_tool(AdvancedCodebaseAnalyzer())


# Auto-register when imported
register_tool()
