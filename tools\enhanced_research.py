"""
Enhanced Research Tool with Web Search and Documentation Integration.

This tool provides comprehensive research capabilities including web search,
documentation lookup, knowledge base integration, and real-time learning.
"""

import logging
import json
import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Generator, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

try:
    from tools.base import ToolSpec, Parameter
    from tools.registry import register_tool
except ImportError:
    # Fallback classes if tools not available
    class ToolSpec:
        def __init__(self, *args, **kwargs):
            self.name = kwargs.get('name', 'enhanced_research')
            self.description = kwargs.get('description', 'Enhanced research tool')
            self.parameters = kwargs.get('parameters', [])
            self.block_types = kwargs.get('block_types', [])

    class Parameter:
        def __init__(self, *args, **kwargs):
            pass

    def register_tool(tool):
        pass

try:
    from message import Message
except ImportError:
    class Message:
        def __init__(self, role, content, **kwargs):
            self.role = role
            self.content = content

try:
    from config import get_config
except ImportError:
    def get_config():
        return type('Config', (), {'data_dir': '/tmp'})()

import time

logger = logging.getLogger(__name__)


class ResearchType(Enum):
    """Types of research queries."""
    WEB_SEARCH = "web_search"
    DOCUMENTATION = "documentation"
    CODE_EXAMPLES = "code_examples"
    BEST_PRACTICES = "best_practices"
    TROUBLESHOOTING = "troubleshooting"
    API_REFERENCE = "api_reference"
    TUTORIALS = "tutorials"
    COMMUNITY = "community"


class SourceType(Enum):
    """Types of information sources."""
    OFFICIAL_DOCS = "official_docs"
    STACKOVERFLOW = "stackoverflow"
    GITHUB = "github"
    BLOG = "blog"
    TUTORIAL = "tutorial"
    VIDEO = "video"
    FORUM = "forum"
    ACADEMIC = "academic"


@dataclass
class ResearchSource:
    """Represents a research source."""
    url: str
    title: str
    content: str
    source_type: SourceType
    relevance_score: float = 0.0
    credibility_score: float = 0.0
    freshness_score: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResearchResult:
    """Comprehensive research result."""
    query: str
    research_type: ResearchType
    sources: List[ResearchSource] = field(default_factory=list)
    summary: str = ""
    key_insights: List[str] = field(default_factory=list)
    code_examples: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    confidence_score: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class EnhancedResearchTool(ToolSpec):
    """
    Enhanced research tool with comprehensive capabilities.
    
    Features:
    - Multi-source web search
    - Documentation integration
    - Code example extraction
    - Best practices identification
    - Real-time learning and caching
    - Source credibility assessment
    """
    
    def __init__(self):
        """Initialize the enhanced research tool."""
        super().__init__(
            name="enhanced_research",
            description="Comprehensive research tool with web search, documentation lookup, and knowledge integration",
            parameters=[
                Parameter(
                    name="query",
                    type="string",
                    description="Research query or question",
                    required=True
                ),
                Parameter(
                    name="research_type",
                    type="string",
                    description="Type of research (web_search, documentation, code_examples, best_practices, troubleshooting, api_reference, tutorials, community)",
                    required=False
                ),
                Parameter(
                    name="max_sources",
                    type="integer",
                    description="Maximum number of sources to retrieve (default: 10)",
                    required=False
                ),
                Parameter(
                    name="include_code",
                    type="boolean",
                    description="Whether to include code examples in results",
                    required=False
                ),
                Parameter(
                    name="language",
                    type="string",
                    description="Programming language or technology focus",
                    required=False
                ),
                Parameter(
                    name="context",
                    type="string",
                    description="Additional context for the research",
                    required=False
                )
            ],
            block_types=["research", "search", "documentation"]
        )
        
        self.cache: Dict[str, ResearchResult] = {}
        self.cache_ttl = timedelta(hours=24)  # Cache for 24 hours
        self.source_patterns = self._initialize_source_patterns()
        self.documentation_apis = self._initialize_doc_apis()
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute the research process."""
        try:
            # Parse parameters
            query = kwargs.get("query", content)
            research_type = ResearchType(kwargs.get("research_type", "web_search"))
            max_sources = int(kwargs.get("max_sources", 10))
            include_code = kwargs.get("include_code", True)
            language = kwargs.get("language", "")
            context = kwargs.get("context", "")
            
            yield Message(
                role="assistant",
                content=f"🔍 **Starting Enhanced Research**\n\n"
                       f"**Query:** {query}\n"
                       f"**Type:** {research_type.value}\n"
                       f"**Language:** {language or 'Any'}\n"
                       f"**Max Sources:** {max_sources}\n\n"
                       f"Searching across multiple sources...\n"
            )
            
            # Check cache first
            cache_key = self._generate_cache_key(query, research_type, language)
            if cache_key in self.cache:
                cached_result = self.cache[cache_key]
                if datetime.now() - cached_result.timestamp < self.cache_ttl:
                    yield Message(
                        role="assistant",
                        content="📋 Using cached research results...\n"
                    )
                    yield from self._format_research_result(cached_result)
                    return
            
            # Perform research
            result = ResearchResult(
                query=query,
                research_type=research_type
            )
            
            # Execute different research strategies based on type
            if research_type == ResearchType.WEB_SEARCH:
                yield from self._web_search(result, query, max_sources, language, context)
            elif research_type == ResearchType.DOCUMENTATION:
                yield from self._documentation_search(result, query, max_sources, language)
            elif research_type == ResearchType.CODE_EXAMPLES:
                yield from self._code_examples_search(result, query, max_sources, language)
            elif research_type == ResearchType.BEST_PRACTICES:
                yield from self._best_practices_search(result, query, max_sources, language)
            elif research_type == ResearchType.TROUBLESHOOTING:
                yield from self._troubleshooting_search(result, query, max_sources, language)
            elif research_type == ResearchType.API_REFERENCE:
                yield from self._api_reference_search(result, query, max_sources, language)
            elif research_type == ResearchType.TUTORIALS:
                yield from self._tutorials_search(result, query, max_sources, language)
            else:  # COMMUNITY
                yield from self._community_search(result, query, max_sources, language)
            
            # Analyze and synthesize results
            yield Message(
                role="assistant",
                content="🧠 Analyzing and synthesizing results...\n"
            )
            
            self._analyze_sources(result)
            self._generate_insights(result)
            self._extract_code_examples(result, include_code)
            self._generate_recommendations(result)
            
            # Cache the result
            self.cache[cache_key] = result
            
            # Format and return results
            yield from self._format_research_result(result)
            
        except Exception as e:
            logger.error(f"Error in enhanced research: {e}")
            yield Message(
                role="assistant",
                content=f"❌ Research error: {str(e)}"
            )
    
    def _web_search(
        self,
        result: ResearchResult,
        query: str,
        max_sources: int,
        language: str,
        context: str
    ) -> Generator[Message, None, None]:
        """Perform comprehensive web search."""
        yield Message(
            role="assistant",
            content="🌐 Searching web sources...\n"
        )
        
        # Enhanced query with context
        enhanced_query = self._enhance_query(query, language, context)
        
        # Search multiple sources
        search_engines = [
            ("Google", self._google_search),
            ("DuckDuckGo", self._duckduckgo_search),
            ("Bing", self._bing_search)
        ]
        
        for engine_name, search_func in search_engines:
            try:
                yield Message(
                    role="assistant",
                    content=f"  📡 Searching {engine_name}...\n"
                )
                
                sources = search_func(enhanced_query, max_sources // len(search_engines))
                result.sources.extend(sources)
                
                yield Message(
                    role="assistant",
                    content=f"  ✅ Found {len(sources)} sources from {engine_name}\n"
                )
                
            except Exception as e:
                logger.warning(f"Search engine {engine_name} failed: {e}")
                yield Message(
                    role="assistant",
                    content=f"  ⚠️ {engine_name} search failed: {str(e)}\n"
                )
    
    def _documentation_search(
        self,
        result: ResearchResult,
        query: str,
        max_sources: int,
        language: str
    ) -> Generator[Message, None, None]:
        """Search official documentation sources."""
        yield Message(
            role="assistant",
            content="📚 Searching documentation sources...\n"
        )
        
        # Search language-specific documentation
        if language:
            doc_sources = self.documentation_apis.get(language.lower(), [])
            for doc_source in doc_sources:
                try:
                    yield Message(
                        role="assistant",
                        content=f"  📖 Searching {doc_source['name']}...\n"
                    )
                    
                    sources = self._search_documentation_api(
                        doc_source,
                        query,
                        max_sources // len(doc_sources)
                    )
                    result.sources.extend(sources)
                    
                except Exception as e:
                    logger.warning(f"Documentation search failed for {doc_source['name']}: {e}")
        
        # Generic documentation search
        generic_sources = self._search_generic_documentation(query, max_sources)
        result.sources.extend(generic_sources)
    
    def _code_examples_search(
        self,
        result: ResearchResult,
        query: str,
        max_sources: int,
        language: str
    ) -> Generator[Message, None, None]:
        """Search for code examples."""
        yield Message(
            role="assistant",
            content="💻 Searching for code examples...\n"
        )
        
        # GitHub search
        github_sources = self._search_github(query, language, max_sources // 2)
        result.sources.extend(github_sources)
        
        # Stack Overflow search
        so_sources = self._search_stackoverflow(query, language, max_sources // 2)
        result.sources.extend(so_sources)
    
    def _enhance_query(self, query: str, language: str, context: str) -> str:
        """Enhance the search query with additional context."""
        enhanced = query
        
        if language:
            enhanced += f" {language}"
        
        if context:
            enhanced += f" {context}"
        
        # Add common programming terms if relevant
        programming_indicators = ["function", "method", "class", "variable", "error", "bug"]
        if any(indicator in query.lower() for indicator in programming_indicators):
            enhanced += " programming code example"
        
        return enhanced
    
    def _google_search(self, query: str, max_results: int) -> List[ResearchSource]:
        """Perform Google search (placeholder implementation)."""
        # This would integrate with Google Custom Search API
        # For now, return mock results
        return [
            ResearchSource(
                url=f"https://example.com/result_{i}",
                title=f"Google Result {i}: {query}",
                content=f"Mock content for {query} from Google search result {i}",
                source_type=SourceType.BLOG,
                relevance_score=0.8,
                credibility_score=0.7
            )
            for i in range(min(max_results, 3))
        ]
    
    def _duckduckgo_search(self, query: str, max_results: int) -> List[ResearchSource]:
        """Perform DuckDuckGo search (placeholder implementation)."""
        # This would integrate with DuckDuckGo API
        return [
            ResearchSource(
                url=f"https://duckduckgo.com/result_{i}",
                title=f"DDG Result {i}: {query}",
                content=f"Mock content for {query} from DuckDuckGo result {i}",
                source_type=SourceType.FORUM,
                relevance_score=0.7,
                credibility_score=0.8
            )
            for i in range(min(max_results, 2))
        ]
    
    def _bing_search(self, query: str, max_results: int) -> List[ResearchSource]:
        """Perform Bing search (placeholder implementation)."""
        # This would integrate with Bing Search API
        return [
            ResearchSource(
                url=f"https://bing.com/result_{i}",
                title=f"Bing Result {i}: {query}",
                content=f"Mock content for {query} from Bing search result {i}",
                source_type=SourceType.TUTORIAL,
                relevance_score=0.75,
                credibility_score=0.75
            )
            for i in range(min(max_results, 2))
        ]
    
    def _search_documentation_api(
        self,
        doc_source: Dict[str, Any],
        query: str,
        max_results: int
    ) -> List[ResearchSource]:
        """Search a specific documentation API."""
        # Placeholder implementation
        return [
            ResearchSource(
                url=f"{doc_source['base_url']}/search?q={query}",
                title=f"{doc_source['name']} Documentation: {query}",
                content=f"Official documentation content for {query}",
                source_type=SourceType.OFFICIAL_DOCS,
                relevance_score=0.9,
                credibility_score=0.95
            )
        ]
    
    def _search_generic_documentation(self, query: str, max_results: int) -> List[ResearchSource]:
        """Search generic documentation sources."""
        # Placeholder implementation
        return [
            ResearchSource(
                url=f"https://docs.example.com/{query}",
                title=f"Generic Documentation: {query}",
                content=f"Generic documentation content for {query}",
                source_type=SourceType.OFFICIAL_DOCS,
                relevance_score=0.8,
                credibility_score=0.9
            )
        ]
    
    def _search_github(self, query: str, language: str, max_results: int) -> List[ResearchSource]:
        """Search GitHub for code examples."""
        # This would integrate with GitHub API
        return [
            ResearchSource(
                url=f"https://github.com/example/repo_{i}",
                title=f"GitHub Example {i}: {query}",
                content=f"```{language}\n# Code example for {query}\nprint('Hello, World!')\n```",
                source_type=SourceType.GITHUB,
                relevance_score=0.85,
                credibility_score=0.8
            )
            for i in range(min(max_results, 3))
        ]
    
    def _search_stackoverflow(self, query: str, language: str, max_results: int) -> List[ResearchSource]:
        """Search Stack Overflow for solutions."""
        # This would integrate with Stack Overflow API
        return [
            ResearchSource(
                url=f"https://stackoverflow.com/questions/{i}",
                title=f"Stack Overflow: {query}",
                content=f"Q: {query}\nA: Here's a solution using {language}...",
                source_type=SourceType.STACKOVERFLOW,
                relevance_score=0.9,
                credibility_score=0.85
            )
            for i in range(min(max_results, 2))
        ]
    
    def _analyze_sources(self, result: ResearchResult) -> None:
        """Analyze and score sources."""
        for source in result.sources:
            # Calculate freshness score
            age_days = (datetime.now() - source.timestamp).days
            source.freshness_score = max(0, 1 - (age_days / 365))  # Decay over a year
            
            # Adjust credibility based on source type
            credibility_multipliers = {
                SourceType.OFFICIAL_DOCS: 1.0,
                SourceType.STACKOVERFLOW: 0.9,
                SourceType.GITHUB: 0.85,
                SourceType.ACADEMIC: 0.95,
                SourceType.TUTORIAL: 0.8,
                SourceType.BLOG: 0.7,
                SourceType.FORUM: 0.6,
                SourceType.VIDEO: 0.75
            }
            
            multiplier = credibility_multipliers.get(source.source_type, 0.7)
            source.credibility_score *= multiplier
        
        # Sort sources by combined score
        result.sources.sort(
            key=lambda s: (s.relevance_score * 0.4 + 
                          s.credibility_score * 0.4 + 
                          s.freshness_score * 0.2),
            reverse=True
        )
    
    def _generate_insights(self, result: ResearchResult) -> None:
        """Generate key insights from sources."""
        # Placeholder implementation - would use NLP/LLM for actual insight extraction
        insights = []
        
        if result.sources:
            insights.append(f"Found {len(result.sources)} relevant sources")
            
            # Analyze source types
            source_types = {}
            for source in result.sources:
                source_types[source.source_type] = source_types.get(source.source_type, 0) + 1
            
            most_common_type = max(source_types, key=source_types.get)
            insights.append(f"Most common source type: {most_common_type.value}")
            
            # Analyze credibility
            avg_credibility = sum(s.credibility_score for s in result.sources) / len(result.sources)
            insights.append(f"Average source credibility: {avg_credibility:.1%}")
        
        result.key_insights = insights
    
    def _extract_code_examples(self, result: ResearchResult, include_code: bool) -> None:
        """Extract code examples from sources."""
        if not include_code:
            return
        
        code_examples = []
        for source in result.sources:
            # Simple code extraction (would be more sophisticated in practice)
            if "```" in source.content:
                # Extract code blocks
                parts = source.content.split("```")
                for i in range(1, len(parts), 2):  # Every other part is code
                    code_examples.append(parts[i].strip())
        
        result.code_examples = code_examples[:5]  # Limit to 5 examples
    
    def _generate_recommendations(self, result: ResearchResult) -> None:
        """Generate recommendations based on research."""
        recommendations = []
        
        if result.sources:
            # Recommend highest-credibility sources
            top_sources = sorted(result.sources, key=lambda s: s.credibility_score, reverse=True)[:3]
            for source in top_sources:
                recommendations.append(f"Check {source.title} ({source.source_type.value})")
            
            # Recommend based on source types
            if any(s.source_type == SourceType.OFFICIAL_DOCS for s in result.sources):
                recommendations.append("Start with official documentation for authoritative information")
            
            if any(s.source_type == SourceType.GITHUB for s in result.sources):
                recommendations.append("Review GitHub examples for practical implementations")
        
        result.recommendations = recommendations
    
    def _format_research_result(self, result: ResearchResult) -> Generator[Message, None, None]:
        """Format and yield the research result."""
        # Summary
        yield Message(
            role="assistant",
            content=f"## 📊 Research Summary\n\n"
                   f"**Query:** {result.query}\n"
                   f"**Sources Found:** {len(result.sources)}\n"
                   f"**Research Type:** {result.research_type.value}\n"
                   f"**Confidence:** {result.confidence_score:.1%}\n\n"
        )
        
        # Key insights
        if result.key_insights:
            yield Message(
                role="assistant",
                content="## 💡 Key Insights\n\n" + 
                       "\n".join(f"• {insight}" for insight in result.key_insights) + "\n\n"
            )
        
        # Top sources
        if result.sources:
            yield Message(
                role="assistant",
                content="## 🔗 Top Sources\n\n"
            )
            
            for i, source in enumerate(result.sources[:5], 1):
                yield Message(
                    role="assistant",
                    content=f"**{i}. {source.title}**\n"
                           f"   🌐 {source.url}\n"
                           f"   📊 Relevance: {source.relevance_score:.1%} | "
                           f"Credibility: {source.credibility_score:.1%}\n"
                           f"   📝 {source.content[:200]}...\n\n"
                )
        
        # Code examples
        if result.code_examples:
            yield Message(
                role="assistant",
                content="## 💻 Code Examples\n\n"
            )
            
            for i, code in enumerate(result.code_examples[:3], 1):
                yield Message(
                    role="assistant",
                    content=f"**Example {i}:**\n```\n{code}\n```\n\n"
                )
        
        # Recommendations
        if result.recommendations:
            yield Message(
                role="assistant",
                content="## 🎯 Recommendations\n\n" +
                       "\n".join(f"• {rec}" for rec in result.recommendations) + "\n\n"
            )
    
    def _generate_cache_key(self, query: str, research_type: ResearchType, language: str) -> str:
        """Generate cache key for research result."""
        return f"{query}_{research_type.value}_{language}".lower().replace(" ", "_")
    
    def _initialize_source_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for identifying source types."""
        return {
            "official_docs": ["docs.", "documentation", "reference", "manual"],
            "stackoverflow": ["stackoverflow.com", "stackexchange.com"],
            "github": ["github.com", "gitlab.com", "bitbucket.org"],
            "tutorial": ["tutorial", "guide", "how-to", "walkthrough"],
            "blog": ["blog", "medium.com", "dev.to"],
            "video": ["youtube.com", "vimeo.com", "video"],
            "forum": ["forum", "community", "discussion"],
            "academic": ["arxiv.org", "acm.org", "ieee.org", "paper"]
        }
    
    def _initialize_doc_apis(self) -> Dict[str, List[Dict[str, str]]]:
        """Initialize documentation API endpoints."""
        return {
            "python": [
                {"name": "Python Docs", "base_url": "https://docs.python.org"},
                {"name": "PyPI", "base_url": "https://pypi.org"}
            ],
            "javascript": [
                {"name": "MDN", "base_url": "https://developer.mozilla.org"},
                {"name": "Node.js Docs", "base_url": "https://nodejs.org/docs"}
            ],
            "java": [
                {"name": "Oracle Java Docs", "base_url": "https://docs.oracle.com/javase"}
            ],
            "rust": [
                {"name": "Rust Docs", "base_url": "https://doc.rust-lang.org"}
            ]
        }

    def is_available(self) -> bool:
        """Check if the enhanced research tool is available."""
        return True


# Register the tool
register_tool(EnhancedResearchTool())

__all__ = [
    "EnhancedResearchTool",
    "ResearchType",
    "SourceType",
    "ResearchSource",
    "ResearchResult",
]
